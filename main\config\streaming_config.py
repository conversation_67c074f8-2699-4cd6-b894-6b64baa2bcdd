#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: streaming_config.py
@time: 2025/7/23 
@Description: 流式输出优化配置
"""
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class StreamingConfig:
    """流式输出配置类"""
    
    # 缓冲区配置
    buffer_size: int = 10  # 缓冲区大小，影响输出频率
    max_buffer_size: int = 50  # 最大缓冲区大小
    flush_interval: float = 0.1  # 强制刷新间隔（秒）
    
    # 事件过滤配置
    event_names: set = None  # 目标节点名称
    target_nodes: set = None  # 目标节点集合
    event_types: set = None  # 关注的事件类型
    
    # 性能优化配置
    enable_metrics: bool = False  # 是否启用性能监控
    enable_caching: bool = True  # 是否启用缓存优化
    cache_ttl: int = 300  # 缓存TTL（秒）
    
    # 流式模式配置
    stream_mode: str = "updates"  # 流式模式: "values", "updates", "debug"
    version: str = "v2"  # astream_events版本
    
    # 错误处理配置
    max_retries: int = 3  # 最大重试次数
    retry_delay: float = 1.0  # 重试延迟（秒）
    
    def __post_init__(self):
        """初始化后处理"""
        if self.event_names is None:
            self.event_names = {"route", "retrieve", "search"}

        if self.target_nodes is None:
            self.target_nodes = {"retrieve", "search"}
        
        if self.event_types is None:
            self.event_types = {"on_chat_model_stream", "on_chain_end"}
    
    @classmethod
    def get_fast_config(cls) -> "StreamingConfig":
        """获取快速响应配置"""
        return cls(
            buffer_size=5,
            flush_interval=0.05,
            enable_caching=True,
            stream_mode="updates"
        )
    
    @classmethod
    def get_balanced_config(cls) -> "StreamingConfig":
        """获取平衡配置"""
        return cls(
            buffer_size=10,
            flush_interval=0.1,
            enable_caching=True,
            enable_metrics=True,
            stream_mode="updates"
        )
    
    @classmethod
    def get_debug_config(cls) -> "StreamingConfig":
        """获取调试配置"""
        return cls(
            buffer_size=1,
            flush_interval=0.01,
            enable_metrics=True,
            stream_mode="debug"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "buffer_size": self.buffer_size,
            "max_buffer_size": self.max_buffer_size,
            "flush_interval": self.flush_interval,
            "event_names": list(self.event_names),
            "target_nodes": list(self.target_nodes),
            "event_types": list(self.event_types),
            "enable_metrics": self.enable_metrics,
            "enable_caching": self.enable_caching,
            "cache_ttl": self.cache_ttl,
            "stream_mode": self.stream_mode,
            "version": self.version,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay
        }


class StreamingOptimizer:
    """流式输出优化器"""
    
    def __init__(self, config: Optional[StreamingConfig] = None):
        self.config = config or StreamingConfig.get_balanced_config()
        self._metrics = {
            "total_chunks": 0,
            "total_chars": 0,
            "total_time": 0.0,
            "avg_chunk_size": 0.0,
            "chars_per_second": 0.0
        }
    
    def update_metrics(self, chunk_size: int, elapsed_time: float):
        """更新性能指标"""
        self._metrics["total_chunks"] += 1
        self._metrics["total_chars"] += chunk_size
        self._metrics["total_time"] += elapsed_time
        
        if self._metrics["total_chunks"] > 0:
            self._metrics["avg_chunk_size"] = (
                self._metrics["total_chars"] / self._metrics["total_chunks"]
            )
        
        if self._metrics["total_time"] > 0:
            self._metrics["chars_per_second"] = (
                self._metrics["total_chars"] / self._metrics["total_time"]
            )
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self._metrics.copy()
    
    def reset_metrics(self):
        """重置性能指标"""
        for key in self._metrics:
            self._metrics[key] = 0.0
    
    def should_flush_buffer(self, buffer_size: int, last_flush_time: float) -> bool:
        """判断是否应该刷新缓冲区"""
        import time
        current_time = time.time()
        
        # 缓冲区大小达到阈值
        if buffer_size >= self.config.buffer_size:
            return True
        
        # 超过强制刷新间隔
        if current_time - last_flush_time >= self.config.flush_interval:
            return True
        
        # 缓冲区达到最大大小
        if buffer_size >= self.config.max_buffer_size:
            return True
        
        return False
    
    def optimize_config_for_model(self, model_name: str) -> StreamingConfig:
        """根据模型类型优化配置"""
        if "gpt-4" in model_name.lower():
            # GPT-4 通常响应较慢，使用较大缓冲区
            return StreamingConfig(
                buffer_size=15,
                flush_interval=0.2,
                enable_caching=True
            )
        elif "gpt-3.5" in model_name.lower():
            # GPT-3.5 响应较快，使用较小缓冲区
            return StreamingConfig(
                buffer_size=8,
                flush_interval=0.1,
                enable_caching=True
            )
        else:
            # 其他模型使用默认配置
            return self.config


# 预定义配置实例
FAST_STREAMING = StreamingConfig.get_fast_config()
BALANCED_STREAMING = StreamingConfig.get_balanced_config()
DEBUG_STREAMING = StreamingConfig.get_debug_config()
