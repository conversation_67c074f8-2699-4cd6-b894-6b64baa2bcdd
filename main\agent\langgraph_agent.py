#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: langgraph_agent.py
@time: 2025/2/7 16:00
@Description: 基于 LangGraph 的智能 RAG Agent
"""

import asyncio
import time
import uuid
from typing import Dict, TypedDict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.checkpoint.memory import MemorySaver
from langfuse.callback import CallbackHandler

# 项目导入
from common.myLog import logger
from core.chain import ChainFactory
from core.tools import TavilySearchResultsTool
from common.cache_manager import global_cache
from core.prompt import prompt_manager


class ProcessingStrategy(Enum):
    """处理策略"""
    RETRIEVAL_ONLY = "retrieval_only"
    SEARCH_ONLY = "search_only"
    PARALLEL_SMART = "parallel_smart"
    ADAPTIVE = "adaptive"


class AgentState(TypedDict, total=False):
    """Agent 状态定义 - LangGraph 的核心"""
    # 输入
    question: str
    session_id: str
    strategy: str
    
    # 路由结果
    target_collection: Optional[str]
    question_type: Optional[str]
    complexity_score: Optional[float]
    
    # 处理结果
    retrieval_result: Optional[Dict[str, Any]]
    search_result: Optional[Dict[str, Any]]
    
    # 决策和输出
    selected_source: Optional[str]  # "retrieval", "search", "hybrid"
    final_answer: Optional[str]
    confidence_score: Optional[float]
    
    # 元数据
    processing_time: Optional[float]
    error_info: Optional[Dict[str, Any]]
    metrics: Optional[Dict[str, Any]]


@dataclass
class NodeMetrics:
    """节点性能指标"""
    total_calls: int = 0
    successful_calls: int = 0
    total_time: float = 0.0
    error_count: int = 0
    
    @property
    def success_rate(self) -> float:
        return self.successful_calls / self.total_calls if self.total_calls > 0 else 0.0
    
    @property
    def avg_time(self) -> float:
        return self.total_time / self.total_calls if self.total_calls > 0 else 0.0
    
    def update(self, success: bool, duration: float):
        self.total_calls += 1
        self.total_time += duration
        if success:
            self.successful_calls += 1
        else:
            self.error_count += 1


class LangGraphRAGAgent:
    """基于 LangGraph 的智能 RAG Agent"""

    _cache_manager = global_cache
    
    def __init__(self, session_id: str = None,  session_state: Dict[str, Any] = None,):
        self.session_id = session_id or str(uuid.uuid4())
        self.session_state = session_state
        self.chain_factory = ChainFactory()
        self.memory = MemorySaver()
        
        # 性能指标
        self.node_metrics = {
            "router": NodeMetrics(),
            "retrieval": NodeMetrics(),
            "search": NodeMetrics(),
            "decision": NodeMetrics()
        }
        
        # 缓存的链
        self._router_chain = None
        self._rag_chains = {}
        self._common_chain = None
        
        # 构建图
        self.graph = self._build_graph()
        
        logger.info(f"LangGraphRAGAgent 初始化完成，session_id: {self.session_id}")
    
    def _build_graph(self) -> CompiledStateGraph:
        """构建 LangGraph 工作流"""
        builder = StateGraph(AgentState)
        
        # 添加节点
        builder.add_node("router", self._router_node)
        builder.add_node("retrieval", self._retrieval_node)
        builder.add_node("search", self._search_node)
        builder.add_node("decision", self._decision_node)
        builder.add_node("error_handler", self._error_handler_node)
        
        # 构建流程
        builder.add_edge(START, "router")
        
        # 路由后的条件分支
        builder.add_conditional_edges(
            "router",
            self._route_strategy,
            {
                "retrieval_only": "retrieval",
                "search_only": "search",
                # "parallel": ["retrieval", "search"],  # 并行执行
                "error": "error_handler"
            }
        )
        
        # 处理完成后都进入决策节点
        builder.add_edge("retrieval", "decision")
        builder.add_edge("search", "decision")
        builder.add_edge("decision", END)
        builder.add_edge("error_handler", END)
        
        return builder.compile(checkpointer=self.memory)
    
    async def _router_node(self, state: AgentState) -> AgentState:
        """路由节点 - 分析问题并确定处理策略"""
        start_time = time.time()
        
        try:
            logger.info(f"路由节点处理问题: {state['question'][:50]}...")
            
            # 获取路由链
            if not self._router_chain:
                self._router_chain = self.chain_factory.create_chain(
                    chain_type="router",
                    session_state=self.session_state
                )
            
            # 执行路由
            target_collection = await self._router_chain.ainvoke({
                "question": state["question"]
            })
            
            # 分析问题复杂度和类型
            question_analysis = self._analyze_question(state["question"])
            
            # 更新状态
            duration = time.time() - start_time
            self.node_metrics["router"].update(True, duration)
            
            return {
                **state,
                "target_collection": target_collection,
                "question_type": question_analysis["type"],
                "complexity_score": question_analysis["complexity"],
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.node_metrics["router"].update(False, duration)
            logger.error(f"路由节点错误: {str(e)}")
            
            return {
                **state,
                "error_info": {
                    "node": "router",
                    "error": str(e),
                    "timestamp": time.time()
                }
            }
    
    async def _retrieval_node(self, state: AgentState) -> AgentState:
        """检索节点"""
        start_time = time.time()
        
        try:
            logger.info(f"检索节点处理，目标集合: {state.get('target_collection')}")
            
            target_collection = state.get("target_collection")
            if not target_collection:
                raise ValueError("缺少目标集合信息")
            
            # 获取 RAG 链
            if target_collection not in self._rag_chains:
                self._rag_chains[target_collection] = self.chain_factory.create_chain(
                    chain_type="rag",
                    retriever_type="Rerank",
                    collection_name=target_collection,
                    session_state=self.session_state
                )
            
            rag_chain = self._rag_chains[target_collection]
            answer = await rag_chain.ainvoke({"question": state["question"]})
            
            # 计算置信度
            confidence = self._calculate_confidence(answer)
            
            duration = time.time() - start_time
            self.node_metrics["retrieval"].update(True, duration)
            
            retrieval_result = {
                "answer": answer,
                "confidence": confidence,
                "source": "retrieval",
                "processing_time": duration,
                "collection": target_collection
            }
            
            return {
                **state,
                "retrieval_result": retrieval_result
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.node_metrics["retrieval"].update(False, duration)
            logger.error(f"检索节点错误: {str(e)}")
            
            return {
                **state,
                "retrieval_result": {
                    "answer": "检索服务暂时不可用",
                    "confidence": 0.0,
                    "source": "retrieval",
                    "processing_time": duration,
                    "error": str(e)
                }
            }
    
    async def _search_node(self, state: AgentState) -> AgentState:
        """搜索节点"""
        start_time = time.time()
        
        try:
            logger.info("搜索节点处理")
            
            # 执行网络搜索
            loop = asyncio.get_event_loop()
            search_results = await loop.run_in_executor(
                None,
                TavilySearchResultsTool,
                state["question"],
                5
            )
            
            # 获取通用链处理搜索结果
            if not self._common_chain:
                self._common_chain = self.chain_factory.create_chain(
                    chain_type="common",
                    prompt=prompt_manager.utils_prompt("search_prompt"),
                    session_state=self.session_state
                )
            
            processed_answer = await self._common_chain.ainvoke({
                "question": state["question"],
                "answer": search_results
            })
            
            duration = time.time() - start_time
            self.node_metrics["search"].update(True, duration)
            
            search_result = {
                "answer": processed_answer,
                "confidence": 0.8,  # 搜索结果通常置信度较高
                "source": "search",
                "processing_time": duration,
                "raw_results": search_results
            }
            
            return {
                **state,
                "search_result": search_result
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.node_metrics["search"].update(False, duration)
            logger.error(f"搜索节点错误: {str(e)}")
            
            return {
                **state,
                "search_result": {
                    "answer": "搜索服务暂时不可用",
                    "confidence": 0.0,
                    "source": "search",
                    "processing_time": duration,
                    "error": str(e)
                }
            }
    
    async def _decision_node(self, state: AgentState) -> AgentState:
        """决策节点 - 选择最佳答案"""
        start_time = time.time()
        
        try:
            logger.info("决策节点处理")
            
            retrieval_result = state.get("retrieval_result")
            search_result = state.get("search_result")
            
            # 智能决策逻辑
            decision = self._make_intelligent_decision(retrieval_result, search_result)
            
            duration = time.time() - start_time
            self.node_metrics["decision"].update(True, duration)
            
            return {
                **state,
                "selected_source": decision["source"],
                "final_answer": decision["answer"],
                "confidence_score": decision["confidence"],
                "processing_time": duration,
                "metrics": {
                    "retrieval_confidence": retrieval_result.get("confidence", 0.0) if retrieval_result else 0.0,
                    "search_confidence": search_result.get("confidence", 0.0) if search_result else 0.0,
                    "decision_reason": decision["reason"]
                }
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.node_metrics["decision"].update(False, duration)
            logger.error(f"决策节点错误: {str(e)}")
            
            return {
                **state,
                "selected_source": "error",
                "final_answer": "抱歉，系统在处理您的问题时遇到了错误，请稍后重试。",
                "confidence_score": 0.0,
                "error_info": {
                    "node": "decision",
                    "error": str(e),
                    "timestamp": time.time()
                }
            }
    
    async def _error_handler_node(self, state: AgentState) -> AgentState:
        """错误处理节点"""
        logger.error(f"进入错误处理节点: {state.get('error_info')}")
        
        return {
            **state,
            "selected_source": "error",
            "final_answer": "抱歉，系统暂时无法处理您的问题，请稍后重试。",
            "confidence_score": 0.0
        }
    
    def _route_strategy(self, state: AgentState) -> str:
        """路由策略决策"""
        if state.get("error_info"):
            return "error"
        
        strategy = state.get("strategy", ProcessingStrategy.RETRIEVAL_ONLY.value)
        complexity = state.get("complexity_score", 0.5)
        question_type = state.get("question_type", "general")

        logger.info(f"路由策略: {strategy}")
        
        if strategy == ProcessingStrategy.RETRIEVAL_ONLY.value:
            return "retrieval_only"
        elif strategy == ProcessingStrategy.SEARCH_ONLY.value:
            return "search_only"
        # elif strategy == ProcessingStrategy.PARALLEL_SMART.value:
        #     return "parallel"
        else:  # ADAPTIVE
            # 自适应策略
            if question_type == "factual" and complexity < 0.3:
                return "retrieval_only"
            elif question_type == "current_events" or complexity > 0.8:
                return "search_only"
            else:
                return END
            # else:
            #     return "parallel"
    
    def _analyze_question(self, question: str) -> Dict[str, Any]:
        """分析问题类型和复杂度"""
        # 简化的问题分析逻辑
        question_lower = question.lower()
        
        # 判断问题类型
        if any(word in question_lower for word in ["最新", "今天", "现在", "当前"]):
            question_type = "current_events"
        elif any(word in question_lower for word in ["什么是", "定义", "概念"]):
            question_type = "factual"
        elif any(word in question_lower for word in ["如何", "怎么", "方法"]):
            question_type = "procedural"
        else:
            question_type = "general"
        
        # 计算复杂度（基于长度和关键词）
        complexity = min(len(question) / 100.0, 1.0)
        if any(word in question_lower for word in ["复杂", "详细", "深入", "全面"]):
            complexity += 0.3
        
        return {
            "type": question_type,
            "complexity": min(complexity, 1.0)
        }
    
    def _calculate_confidence(self, answer: str) -> float:
        """计算答案置信度"""
        if any(keyword in answer for keyword in ["未学习", "不知道", "无法回答", "抱歉"]):
            return 0.1
        elif any(keyword in answer for keyword in ["可能", "大概", "也许", "估计"]):
            return 0.6
        elif len(answer) < 50:
            return 0.4
        elif len(answer) > 200:
            return 0.9
        else:
            return 0.7
    
    def _make_intelligent_decision(self, retrieval_result: Dict, search_result: Dict) -> Dict[str, Any]:
        """智能决策逻辑"""
        if not retrieval_result and not search_result:
            return {
                "source": "error",
                "answer": "系统无法获取有效结果",
                "confidence": 0.0,
                "reason": "both_failed"
            }
        
        if not retrieval_result:
            return {
                "source": "search",
                "answer": search_result["answer"],
                "confidence": search_result["confidence"],
                "reason": "retrieval_failed"
            }
        
        if not search_result:
            return {
                "source": "retrieval",
                "answer": retrieval_result["answer"],
                "confidence": retrieval_result["confidence"],
                "reason": "search_failed"
            }
        
        # 两个结果都存在，智能选择
        retrieval_conf = retrieval_result.get("confidence", 0.0)
        search_conf = search_result.get("confidence", 0.0)
        
        if retrieval_conf >= 0.8:
            return {
                "source": "retrieval",
                "answer": retrieval_result["answer"],
                "confidence": retrieval_conf,
                "reason": "high_retrieval_confidence"
            }
        elif search_conf >= 0.8:
            return {
                "source": "search",
                "answer": search_result["answer"],
                "confidence": search_conf,
                "reason": "high_search_confidence"
            }
        elif abs(retrieval_conf - search_conf) < 0.1 and min(retrieval_conf, search_conf) > 0.5:
            # 混合答案
            hybrid_answer = f"""基于知识库的回答：
{retrieval_result['answer']}

网络搜索补充信息：
{search_result['answer']}"""
            return {
                "source": "hybrid",
                "answer": hybrid_answer,
                "confidence": max(retrieval_conf, search_conf),
                "reason": "hybrid_high_confidence"
            }
        elif retrieval_conf > search_conf:
            return {
                "source": "retrieval",
                "answer": retrieval_result["answer"],
                "confidence": retrieval_conf,
                "reason": "retrieval_higher_confidence"
            }
        else:
            return {
                "source": "search",
                "answer": search_result["answer"],
                "confidence": search_conf,
                "reason": "search_higher_confidence"
            }

    async def process_question(
        self,
        question: str,
        strategy: ProcessingStrategy = ProcessingStrategy.RETRIEVAL_ONLY
    ) -> Dict[str, Any]:
        """处理问题 - 主要接口"""
        start_time = time.time()

        # 构建初始状态
        initial_state = {
            "question": question,
            "session_id": self.session_id,
            "strategy": strategy.value
        }

        # 配置
        config = {
            "configurable": {
                "thread_id": self.session_id,
                "checkpoint_ns": f"session_{self.session_id}"
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "session_id": self.session_id,
                "question": question[:100]
            }
        }

        try:
            # 执行图
            result = await self.graph.ainvoke(initial_state, config)

            total_time = time.time() - start_time

            return {
                "answer": result.get("final_answer", "无法生成答案"),
                "confidence": result.get("confidence_score", 0.0),
                "source": result.get("selected_source", "unknown"),
                "processing_time": total_time,
                "target_collection": result.get("target_collection"),
                "question_type": result.get("question_type"),
                "complexity_score": result.get("complexity_score"),
                "metrics": result.get("metrics", {}),
                "session_id": self.session_id
            }

        except Exception as e:
            logger.error(f"处理问题失败: {str(e)}", exc_info=True)
            return {
                "answer": "抱歉，系统处理您的问题时遇到错误，请稍后重试。",
                "confidence": 0.0,
                "source": "error",
                "processing_time": time.time() - start_time,
                "error": str(e),
                "session_id": self.session_id
            }

    async def stream_response(
        self,
        question: str,
        strategy: ProcessingStrategy = ProcessingStrategy.RETRIEVAL_ONLY
    ):
        """流式响应 - 基于 LangGraph 的 astream_events"""
        # 构建初始状态
        initial_state = {
            "question": question,
            "session_id": self.session_id,
            "strategy": strategy.value
        }

        # 配置
        config = {
            "configurable": {
                "thread_id": self.session_id,
                "checkpoint_ns": f"session_{self.session_id}",
                "streaming": True
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "session_id": self.session_id,
                "question": question[:100]
            }
        }

        # 状态追踪
        current_node = None
        chain_end_handled = False
        buffer = []

        try:
            yield f"🤔 开始处理问题: {question[:50]}{'...' if len(question) > 50 else ''}\n\n"

            async for event in self.graph.astream_events(
                initial_state,
                config,
                version="v2"
            ):
                event_type = event.get("event")
                event_name = event.get("name", "")

                # 节点开始事件
                if event_type == "on_chain_start" and event_name in ["router", "retrieval", "search", "decision"]:
                    current_node = event_name
                    node_emoji = {
                        "router": "🧭",
                        "retrieval": "📚",
                        "search": "🔍",
                        "decision": "🎯"
                    }
                    yield f"{node_emoji.get(current_node, '⚙️')} {current_node.title()} 节点开始处理...\n"

                # 节点完成事件
                elif event_type == "on_chain_end" and event_name in ["router", "retrieval", "search", "decision"]:
                    if current_node == event_name:
                        output = event.get("data", {}).get("output", {})

                        if event_name == "router":
                            target = output.get("target_collection", "未知")
                            q_type = output.get("question_type", "未知")
                            yield f"✅ 路由完成 - 目标: {target}, 类型: {q_type}\n\n"

                        elif event_name in ["retrieval", "search"]:
                            result = output.get(f"{event_name}_result", {})
                            confidence = result.get("confidence", 0.0)
                            yield f"✅ {event_name.title()} 完成 - 置信度: {confidence:.2f}\n"

                        elif event_name == "decision":
                            source = output.get("selected_source", "未知")
                            confidence = output.get("confidence_score", 0.0)
                            yield f"✅ 决策完成 - 选择: {source}, 置信度: {confidence:.2f}\n\n"

                # LLM 流式输出
                elif event_type == "on_chat_model_stream":
                    chunk = event.get("data", {}).get("chunk", {})
                    if hasattr(chunk, 'content') and chunk.content:
                        buffer.append(chunk.content)

                        # 缓冲输出
                        if len(buffer) >= 5:
                            content = "".join(buffer)
                            buffer.clear()
                            yield content
                        chain_end_handled = True

                # 图完成事件
                elif event_type == "on_chain_end" and event_name == "LangGraph":
                    # 输出剩余缓冲内容
                    if buffer:
                        yield "".join(buffer)
                        buffer.clear()

                    # 输出最终结果
                    if not chain_end_handled:
                        output = event.get("data", {}).get("output", {})
                        final_answer = output.get("final_answer")
                        if final_answer:
                            yield f"\n\n🎉 最终答案:\n{final_answer}\n"

                    # 输出统计信息
                    processing_time = output.get("processing_time", 0.0)
                    source = output.get("selected_source", "未知")
                    yield f"\n📊 处理完成 | 来源: {source} | 耗时: {processing_time:.2f}秒"

            # 确保所有缓冲内容都被输出
            if buffer:
                yield "".join(buffer)

        except Exception as e:
            logger.error(f"流式处理错误: {str(e)}")
            yield f"\n❌ 处理出错: {str(e)}\n"
            yield "抱歉，系统暂时无法处理您的问题，请稍后重试。"

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "node_metrics": {
                name: {
                    "total_calls": metrics.total_calls,
                    "success_rate": metrics.success_rate,
                    "avg_time": metrics.avg_time,
                    "error_count": metrics.error_count
                }
                for name, metrics in self.node_metrics.items()
            },
            "session_id": self.session_id,
            "graph_info": {
                "nodes": list(self.graph.nodes.keys()),
                "edges": len(self.graph.edges)
            }
        }

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        total_calls = sum(m.total_calls for m in self.node_metrics.values())
        total_errors = sum(m.error_count for m in self.node_metrics.values())

        overall_success_rate = 1.0 - (total_errors / total_calls) if total_calls > 0 else 1.0

        # 确定整体状态
        if overall_success_rate >= 0.95:
            status = "healthy"
        elif overall_success_rate >= 0.8:
            status = "degraded"
        else:
            status = "unhealthy"

        return {
            "status": status,
            "overall_success_rate": overall_success_rate,
            "total_requests": total_calls,
            "total_errors": total_errors,
            "node_status": {
                name: "healthy" if metrics.success_rate >= 0.8 else "degraded"
                for name, metrics in self.node_metrics.items()
                if metrics.total_calls > 0
            },
            "session_id": self.session_id
        }

    async def get_conversation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取对话历史 - 利用 LangGraph 的检查点功能"""
        try:
            config = {
                "configurable": {
                    "thread_id": self.session_id,
                    "checkpoint_ns": f"session_{self.session_id}"
                }
            }

            # 获取检查点历史
            history = []
            async for checkpoint in self.graph.aget_state_history(config, limit=limit):
                if checkpoint.values:
                    history.append({
                        "question": checkpoint.values.get("question"),
                        "answer": checkpoint.values.get("final_answer"),
                        "confidence": checkpoint.values.get("confidence_score"),
                        "source": checkpoint.values.get("selected_source"),
                        "timestamp": checkpoint.created_at
                    })

            return history

        except Exception as e:
            logger.error(f"获取对话历史失败: {str(e)}")
            return []

    async def clear_conversation_history(self):
        """清除对话历史"""
        try:
            # 重新初始化内存
            self.memory = MemorySaver()
            self.graph = self._build_graph()
            logger.info(f"已清除 session {self.session_id} 的对话历史")
        except Exception as e:
            logger.error(f"清除对话历史失败: {str(e)}")

    def visualize_graph(self, output_path: str = "graph.png"):
        """可视化图结构 - LangGraph 的优势功能"""
        try:
            # 尝试获取图的可视化
            if hasattr(self.graph, 'get_graph'):
                graph_image = self.graph.get_graph().draw_mermaid_png()
                with open(output_path, "wb") as f:
                    f.write(graph_image)
                logger.info(f"图结构已保存到: {output_path}")
            else:
                logger.warning("当前 LangGraph 版本不支持图可视化")

        except ImportError:
            logger.warning("需要安装 graphviz 才能使用图可视化功能")
        except Exception as e:
            logger.error(f"图可视化失败: {str(e)}")


# 使用示例
if __name__ == "__main__":
    ...