#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: style.py 
@time: 2024/11/12 11:26
@Description: 
"""

centered_css = """
    <style>                   
        .body-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 79vh;
            margin: -80px
        }
        a:hover {
            text-decoration: none;
        }        
        .st-emotion-cache-1rsyhoq a {
            color: white;
        }
        .bt-link {
            background: #0089ff;
            color: white; 
            padding: 10px 20px; 
            border-radius: 12px;
            text-decoration: none; 
            font-size: 18px; 
            font-weight: 600;
            display: inline-block; 
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.2s ease; 
            margin-bottom: 20px;
        }
        .ym-container {
            margin-top:9%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom:-9%;
        }
        .st-emotion-cache-1rsyhoq p {
            display: none;
        }
        .footer-fixed {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            border-top: 1px solid #e2e8f0;
            padding: 20px;
            text-align: center;
            z-index: 1000;
        }
        .main-content {
            margin-bottom: 100px;
        }
    </style>
"""


index_css = """
    <style>
        .index-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 79vh;
            margin: -80px
        }
        .ym-container {
            margin-top:12%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom:-12%;
        }
        a:hover {
            text-decoration: none;
        }
    </style>
"""


statistical_title_css = """
    <style>
        .statistical-title {
            text-align: center;
            color: red;
        }
    </style>
"""


ym_footer_css = """
    <style>
        .ym-footer {
            margin-top:120%;
            display: flex;
            align-items: flex-end;
        }
    </style>
"""