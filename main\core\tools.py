#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: tools.py 
@time: 2025/2/17 16:26
@Description: 
"""
import os
os.environ["TAVILY_API_KEY"] = '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'

from dotenv import load_dotenv
from langchain_community.tools import TavilySearchResults
from langchain_community.utilities import SerpAPIWrapper
from tenacity import retry, stop_after_attempt, wait_exponential

from common.myLog import logger
from common.log_optimizer import performance_logger, log_error, log_info

load_dotenv()

logger.name = __name__

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
@performance_logger(threshold=5.0)
def GoogleSearchResultsTool(
        question: str
) -> str:
    try:
        log_info("开始Google搜索", query=question[:50] + "..." if len(question) > 50 else question)
        result = SerpAPIWrapper(
                serpapi_api_key=os.getenv("GOOGLE_API_KEY"),
                params={
                    "gl":"cn",
                    "hl":"zh-CN",
                }
        ).run(question)
        log_info("Google搜索完成", result_length=len(result) if result else 0)
        return result
    except Exception as e:
        log_error("Google搜索失败", error=e, query=question[:50] + "..." if len(question) > 50 else question)
        return "搜索服务暂时不可用，请稍后重试。"


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
@performance_logger(threshold=5.0)
def TavilySearchResultsTool(
        question: str,
        max_results: int = 10
) -> str:
    try:
        log_info("开始Tavily搜索",
                query=question[:50] + "..." if len(question) > 50 else question,
                max_results=max_results)
        results = TavilySearchResults(
            max_results=max_results,
            include_answer=True
        ).run(question)

        combined_content = ''.join([i["content"] for i in results])
        log_info("Tavily搜索完成",
                results_count=len(results),
                content_length=len(combined_content))
        return combined_content
    except Exception as e:
        log_error("Tavily搜索失败", error=e,
                 query=question[:50] + "..." if len(question) > 50 else question,
                 max_results=max_results)
        return "搜索服务暂时不可用，请稍后重试。"
