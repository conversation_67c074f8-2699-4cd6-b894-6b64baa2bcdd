#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: rerank.py 
@time: 2025/8/12 18:08
@Description: 
"""
import json
from typing import Sequence, Optional

# from sentence_transformers import CrossEncoder
#
# model_path = r'D:\Work\code\ym_agent\main\rerank_model'
# # 加载 HuggingFace 的 BGE reranker 模型
# # model = CrossEncoder(model_path, max_length=512)
# #
# query = "值班"
# documents = [
# "问题描述：线上问题值班人员，处理结果：星期一 ： 王怀堂 \n 星期二 ： 孙琳 \n 星期三 ： 王婷 何静 \n 星期四 ： 林静文 蒲天田 \n 星期五 ： 赵乔 \n 星期六 ： 王怀堂 \n 星期日 ： 王怀堂 \n 节假日 ： 王怀堂",
# "问题描述：维护门诊经纬信息，无法编辑，处理结果：经纬度搞反了，经度（-180,180），纬度（-90,90）",
# "问题描述：门诊主页地图导航错误，处理结果：可能门诊配置的经纬度不正确，使用腾讯地图坐标拾取器重新定位后修改门诊经纬度",
# "问题描述: 9-45 周岁，系统是按照用户身份证生日进行判定，还是就是某一年不管生日是几月都算 45 周岁内，处理结果：按生日计算年龄，且 45 岁未满 46 岁仍可以预约",
# "问题描述：门诊主页后为什么看不到四九价信息？，处理结果：截图是 H5 链接，H5 页面没有订阅功能，不会显示需要订阅的疫苗"
# ]
# #
# # # 生成 query 和 document 的配对评分
# # pairs = [[query, doc] for doc in documents]
# # scores = model.predict(pairs)
# #
# # # 排序
# # reranked_docs = [doc for _, doc in sorted(zip(scores, documents), reverse=True)]
# # print(reranked_docs)
#
#
# from FlagEmbedding import FlagReranker
#
# # 初始化Reranker，指定本地模型路径
# reranker = FlagReranker(model_path, use_fp16=True)
#
# # 定义查询和文档
# # query = "What is Deep Learning?"
# # documents = [
# #     "Deep Learning is a subset of machine learning involving neural networks.",
# #     "Hello, this is unrelated text."
# # ]
#
# # 构造查询-文档对
# pairs = [[query, doc] for doc in documents]
#
# # 计算相关性得分
# scores = reranker.compute_score(pairs, normalize=True)
# print(scores)  # 输出：[0.99729556, 0.00009387641]

from langchain.retrievers import ContextualCompressionRetriever
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_core.callbacks import Callbacks


# 1. 自定义 Ollama Reranker 压缩器
from langchain.retrievers.document_compressors.base import BaseDocumentCompressor
from langchain.schema import Document
import requests
from pydantic import PrivateAttr


class OllamaReranker(BaseDocumentCompressor):
    """基于 Ollama API 的文档重排序器（Reranker）"""
    _model_name: str = PrivateAttr()
    _top_n: int = PrivateAttr()

    def __init__(self, model_name: str, top_n: int = 3, **kwargs):
        super().__init__(**kwargs)
        self._model_name = model_name
        self._top_n = top_n

    def compress_documents(
        self,
        documents: Sequence[Document],
        query: str,
        callbacks: Optional[Callbacks] = None,
    ) -> Sequence[Document]:
        if not documents:
            return []

        # 构造提示，让 Ollama 输出严格 JSON
        prompt = {
            "query": query,
            "documents": [doc.page_content for doc in documents],
            "instruction": (
                "You are a reranker model. Rank the documents in order of relevance to the query. "
                "Return a JSON array of objects, each with keys: index (1-based), score (float). "
                "Example: [{\"index\":1,\"score\":0.98},{\"index\":2,\"score\":0.85}]"
            )
        }

        # 调用 Ollama API
        resp = requests.post(
            "http://************:11434/api/generate",
            json={
                "model": self._model_name,
                "prompt": json.dumps(prompt, ensure_ascii=False),
                "stream": False
            },
            timeout=120
        )
        resp.raise_for_status()
        output_text = resp.json().get("response", "").strip()

        # 解析 JSON 输出
        try:
            ranked = json.loads(output_text)
            if not isinstance(ranked, list):
                raise ValueError("返回的 JSON 不是列表")

            top_docs = []
            for r in sorted(ranked, key=lambda x: x["score"], reverse=True)[:self._top_n]:
                idx = r["index"] - 1
                if 0 <= idx < len(documents):
                    top_docs.append(documents[idx])
            return top_docs
        except Exception as e:
            print(f"⚠️ JSON 解析失败 ({e})，返回前 {self._top_n} 条文档。原输出：", output_text)
            return documents[:self._top_n]


if __name__ == '__main__':
    ...
