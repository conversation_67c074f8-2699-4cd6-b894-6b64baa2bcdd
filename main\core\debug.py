#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: debug.py 
@time: 2025/8/12 14:41
@Description: 
"""
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import CrossEncoderReranker
from langchain_community.cross_encoders import HuggingFaceCrossEncoder
from langchain_milvus import Mi<PERSON>vu<PERSON>
from langchain_openai.embeddings import OpenAIEmbeddings
from main.core.rerank import OllamaReranker
from sentence_transformers import CrossEncoder

model_path = r'D:\Work\code\ym_agent\main\rerank_model'

embedding = OpenAIEmbeddings(
    base_url="http://************:9998/v1",
    api_key="qwe",
    model="bge-large-zh-v1.5"
)

connection = {
            "uri": "http://************:19530",
            "user": "root",
            "password": "<PERSON><PERSON><PERSON><PERSON>",
            "db_name": "YM",
        }

vectorstore = Milvus(embedding_function=embedding, connection_args=connection, collection_name='CSQ')

retriever = vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": int(5)}
            )

cross_encoder = HuggingFaceCrossEncoder(model_name=model_path)
# compressor = CrossEncoderReranker(model=cross_encoder, top_n=5)
compressor = OllamaReranker(model_name='qllama/bce-reranker-base_v1:latest', top_n=3)
def create_compression_retriever() -> ContextualCompressionRetriever:
    return ContextualCompressionRetriever(
        base_retriever=retriever,
        base_compressor=compressor
    )

if __name__ == '__main__':
    retriever1 = create_compression_retriever()
    print(retriever1.invoke("公号无法访问"))
