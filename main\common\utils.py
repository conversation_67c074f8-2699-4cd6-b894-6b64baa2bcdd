#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: utils.py 
@time: 2024/9/25 17:55
@Description: 
"""
import json
import os
import re
import time
from datetime import datetime, timedelta
from functools import wraps, lru_cache
from typing import List, Dict, Any

import streamlit as st
from langchain_core.documents import Document
from markupsafe import escape
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_openai import ChatOpenAI
# from langfuse.callback import CallbackHandler
from langfuse.langchain import CallbackHandler

from config.config_center import settings
from common.myLog import logger

logger.name = __name__

def read_txt_file(file_path):
    """
    :param file_path:
    :return:
    """
    with open(file_path, 'a+', encoding='utf-8') as file:
        file.seek(0)
        content = file.read()
        reversed_lines = content.splitlines()[::-1]
        reversed_string = '\n'.join(reversed_lines)
    return reversed_string

def count_lines(content):
    """
    :param content:
    :return:
    """
    lines = re.split(r'\s+', content.strip())  # 使用空白字符分割文本
    count_dict = {}
    for line in lines:
        if line in count_dict:
            count_dict[line] += 1
        else:
            count_dict[line] = 1
    return count_dict


def get_top_three_lines(file_path, top):
    """
    :param file_path:
    :param top:
    :return:
    """
    content = read_txt_file(file_path)
    if content:
        count_dict = count_lines(content)
        sorted_lines = sorted(count_dict.items(), key=lambda x: x[1], reverse=True)
        return sorted_lines[:top]
    else:
        return settings.business.Default_TOP


def get_save_dir():
    return settings.paths.DOCS_PATH

def save_uploaded_file(uploaded_file: bytes, save_dir: str):
    try:
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        path = os.path.join(save_dir, uploaded_file.name)
        with open(path, "wb") as f:
            f.write(uploaded_file.getbuffer())
            return path
    except Exception as e:
        print(f"Error saving upload to disk: {e}")


def stream_output(llm, input_message):
    stream = llm.stream(
        input_message,
        config={
            "callbacks":[CallbackHandler()]
        }
    )
    for chunk in stream:
        yield chunk


def stream_output_com(
        model: ChatOpenAI,
        input_message: str,
        session_id: str,
        retriever: VectorStoreRetriever = None,
        ai_type: str = "OTHER"
):
    argument = {"question": input_message} if ai_type == "TSL" else {"query": input_message, "context": retriever.invoke(input_message)}
    stream = model.stream(
        argument,
        config={
            "callbacks":[CallbackHandler()],
            "configurable": {
                "session_id": session_id
            },
            "metadata": {
                "langfuse_user_id": session_id,
            }
        }
    )
    for chunk in stream:
        yield chunk


def stream_output_history(
        model: ChatOpenAI,
        input_message: str,
        session_id: str,
        ai_type: str = "Y",
        doc_content: str = None,
):
    logger.info(f"正在处理消息类型: {ai_type}")
    argument = {"question": input_message} if ai_type == "Y" else {"question": input_message, "doc_content": doc_content}
    logger.info(f"{argument=}")
    stream = model.stream(
        argument,
        config={
            "callbacks":[CallbackHandler()],
            "configurable": {
                "session_id": session_id,
            },
            "metadata": {
                "langfuse_user_id": session_id,
            }
        }
    )
    for chunk in stream:
        yield chunk


def format_context(docs: List[Document]) -> str:
    """上下文格式化"""
    return "\n\n".join(
        f"信息来源：{doc.metadata['source']}\n{doc.page_content}"
        for doc in docs
    )


def ExtractTestCase(case_data: str) -> List[Dict[str, Any]]:
    """提取测试用例"""
    try:
        pattern = r"```([\s\S]*?)```"
        matches = re.findall(pattern, case_data)
        data = matches[0].replace('\n', "").replace(" ", "")[4:]

    except:
        data = case_data.replace('\n', "")

    return json.loads(data)

def extract_unique_parent_dept_ids(
        parent_list: list
) -> list:
    result_set = set()
    for item in parent_list:
        if isinstance(item, dict) and "parent_dept_id_list" in item:
            result_set.update(item["parent_dept_id_list"])
    return list(result_set)


def SerializedData(data):
    result = [user.__dict__ for user in data]
    for item in result: item.pop('_sa_instance_state', None)
    return result


@lru_cache(maxsize=settings.MAX_SIZE)
def get_system_avatar() -> str:
    """获取系统头像（带缓存）"""
    return settings.paths.SYSTEM_LOG0_PATH


def safe_markdown(content: str, allow_html: bool = False, **kwargs):
    if allow_html:
        return st.markdown(content, unsafe_allow_html=True, **kwargs)
    else:
        return st.markdown(escape(content), unsafe_allow_html=False, **kwargs)

def error_handler(func):
    """增强的错误处理装饰器，提供更详细的日志记录"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        start_time = time.time()

        try:
            logger.info(f"开始执行: {func_name}")
            result = func(*args, **kwargs)

            execution_time = time.time() - start_time
            logger.info(f"执行完成: {func_name}, 耗时: {execution_time:.3f}s")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"执行失败: {func_name}, 耗时: {execution_time:.3f}s, 错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            st.error(f"操作失败: {str(e)}")
            raise  # 重新抛出异常以便上层处理
    return wrapper

def with_loading(message: str = "处理中..."):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with st.spinner(message):
                logger.info(f"正在初始化模型!")
                return func(*args, **kwargs)
        return wrapper
    return decorator

def measure_time(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        duration = time.perf_counter() - start
        logger.info(f"{func.__name__} 执行时间: {duration:.2f}秒")
        return result
    return wrapper


def get_time_period() -> str:
    hour = datetime.now().hour
    return {
        hour < 5: "凌晨好",
        5 <= hour < 8: "早安",
        8 <= hour < 12: "上午好",
        12 <= hour < 14: "中午好",
        14 <= hour < 18: "下午好",
        18 <= hour < 20: "傍晚好",
        20 <= hour < 23: "晚上好",
        hour >= 23: "深夜好"
    }[True]


def create_image(chain, num):
    image_data = chain.get_graph(xray=False).draw_mermaid_png()

    # 保存图像到本地
    with open(f'output_image{num}.png', 'wb') as f:
        f.write(image_data)


def remove_empty_lines(text: str) -> str:
    """移除文本中的连续空白行，保留单个换行符"""
    return re.sub(r'\n\s*\n', '\n', text).strip()


now_time = datetime.now()

Year = datetime.strftime(now_time, "%Y")

Month = datetime.strftime(now_time, "%m")

Day = datetime.strftime(now_time, "%d")

start_date = now_time - timedelta(days=30)

if __name__ == '__main__':
    pass