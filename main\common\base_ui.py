#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: base_ui.py
@time: 2025/4/15 10:00
@Description: UI组件基类
"""
from typing import List, Dict, Any

import streamlit as st

from common.utils import get_system_avatar, remove_empty_lines
from config.config_center import settings


class BaseUI:
    """UI组件基类"""
    
    def __init__(self, session_key: str, welcome_message: str):
        """初始化UI基类
        
        Args:
            session_key: 会话状态的键名
            welcome_message: 欢迎消息内容
        """
        self.session_key = session_key
        self.welcome_message = welcome_message
        self._init_session_state()

    def _init_session_state(self):
        """初始化会话状态"""
        if self.session_key not in st.session_state:
            st.session_state[self.session_key] = [
                {
                    "role": "assistant",
                    "avatar": get_system_avatar(),
                    "content": self.welcome_message
                }
            ]

            # 只保留最新的10条消息
            if len(st.session_state[self.session_key]) > 10:
                st.session_state[self.session_key] = st.session_state[self.session_key][-10:]

    async def render_header(self, title: str):
        """渲染页面头部
        
        Args:
            title: 页面标题
        """
        rows = st.columns([7, 3])
        with rows[0]:
            st.markdown(
                f"""### {title}<span style='color: {settings.ui.global_color}'>智能</span>助手""",
                unsafe_allow_html=True
            )
        with rows[1]:
            st.session_state["temperature"] = st.slider(
                "温度设置",
                min_value=0.0,
                max_value=1.0,
                value=0.7,
                step=0.1,
                help="温度较低的值会产生更确定性的结果，较高的值会产生更多样化的结果"
            )
        st.divider()

    async def render_chat_history(self):
        """渲染聊天历史"""
        for message in st.session_state[self.session_key]:
            with st.chat_message(message["role"], avatar=message.get("avatar", None)):
                st.markdown(message["content"])

    async def handle_user_input(self, prompt: str):
        """处理用户输入
        
        Args:
            prompt: 用户输入内容
        """
        prompt = remove_empty_lines(prompt)

        with st.chat_message("user"):
            st.markdown(prompt)

        st.session_state[self.session_key].append({"role": "user", "content": prompt})
        
    def add_assistant_message(self, content: str):
        """添加助手消息到历史记录
        
        Args:
            content: 消息内容
        """
        st.session_state[self.session_key].append(
            {
                "role": "assistant",
                "avatar": get_system_avatar(),
                "content": content
            }
        ) 