#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: config_center.py
@time: 2025/01/20 16:31
@Description:
"""
import socket
import os
from typing import Dict, List, Tuple, Any
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
from functools import lru_cache
from dotenv import load_dotenv

load_dotenv()

def get_local_ip():
    try:
        hostname = socket.gethostname()
        ip_addresses = socket.gethostbyname_ex(hostname)[2]
        for ip in ip_addresses:
            if not ip.startswith('127.') and ip.startswith('192.') and ip.count('.') == 3:
                return ip
        return "127.0.0.1"
    except Exception as e:
        print(f"Error occurred while fetching the IP address: {e}")
        return None

class PathConfig(BaseModel):
    """路径相关配置"""
    BASE_DIR: str = Field(default_factory=lambda: os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    @property
    def ICON_PATH(self) -> str:
        path = os.path.join(self.BASE_DIR, "icon")
        os.makedirs(path, exist_ok=True)
        return path
    
    @property
    def DOCS_PATH(self) -> str:
        path = os.path.join(self.BASE_DIR, "documents")
        os.makedirs(path, exist_ok=True)
        return path
        
    @property
    def LOG_PATH(self) -> str:
        path = os.path.join(self.BASE_DIR, "logs")
        os.makedirs(path, exist_ok=True)
        return path
        
    @property
    def CHROMADB_DIRECTORY(self) -> str:
        path = os.path.join(self.BASE_DIR, "chroma_emd")
        os.makedirs(path, exist_ok=True)
        return path
        
    @property
    def RERANK_MODEL_PATH(self) -> str:
        path = os.path.join(self.BASE_DIR, "rerank_model")
        os.makedirs(path, exist_ok=True)
        return path

    @property
    def EMBEDDING_MODEL_PATH(self) -> str:
        path = os.path.join(self.BASE_DIR, "embedding_model")
        os.makedirs(path, exist_ok=True)
        return path
        
    @property
    def HISTORY_DB_PATH(self) -> str:
        return ''.join(["sqlite:///", os.path.join(self.BASE_DIR, "config", "history.db")])
        
    @property
    def SQl_CACHE_PATH(self) -> str:
        return os.path.join(self.BASE_DIR, "config", "cache.db")

    @property
    def SYSTEM_LOG0_PATH(self) -> str:
        return os.path.join(self.ICON_PATH, "system.png")

class APIConfig(BaseModel):
    """API配置管理"""

    env: str = Field(default="development")

    BASE_URL_CONFIG: Dict[str, Dict[str, str]] = Field(default_factory=lambda: {
        "dev": {
            "localIp": f"http://{get_local_ip()}:8899"
        },
        "prod": {
            "localIp": "https://ymchat.scmttec.com"
        }
    })

    @property
    def BASE_URL(self) -> str:
        return self.BASE_URL_CONFIG[self.env]["localIp"]

class DatabaseConfig(BaseModel):
    """数据库相关配置"""
    # 添加 env 字段
    env: str = Field(default="development")

    # Redis配置
    REDIS_CONFIG: Dict[str, Dict[str, str]] = Field(default_factory=lambda: {
        "dev": {
            "url": "redis://:123456@**************:3202/0"
        },
        "prod": {
            "url": "redis://:123456@**************:3203/0"
        }
    })

    # Milvus配置
    MILVUS_CONFIG: Dict[str, Dict[str, Any]] = Field(default_factory=lambda: {
        "dev": {
            "uri": "http://************:19530",
            "user": "root",
            "password": "Milvus",
            "db_name": "YM",
        },
        "prod": {
            "uri": "http://************:19530",
            "user": "root",
            "password": "Milvus",
            "db_name": "YM",
        }
    })

    # MySQL配置
    DATABASE_CONFIG: Dict[str, Dict[str, str]] = Field(default_factory=lambda: {
        "dev": {
            "url": "mysql+pymysql://root:ymsonic@192.168.10.154:3306/ym_rag_test"
        },
        "prod": {
            "url": "mysql+pymysql://root:ymsonic@192.168.10.154:3306/ym_rag"
        }
    })

    @field_validator("env")
    def validate_env(cls, v: str) -> str:
        allowed = {"dev", "prod"}
        if v not in allowed:
            raise ValueError(f"env must be one of {allowed}")
        return v
    @property
    def REDIS_URL(self) -> str:
        """获取当前环境的Redis URL"""
        return self.REDIS_CONFIG[self.env]["url"]

    @property
    def MILVUS_DB(self) -> dict[str, Any]:
        """获取当前环境的Milvus数"""
        return self.MILVUS_CONFIG[self.env]

    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        """获取当前环境的数据库URI"""
        return self.DATABASE_CONFIG[self.env]["url"]

class DingTalkConfig(BaseModel):
    """钉钉相关配置"""
    api_url: str = "https://api.dingtalk.com"
    oapi_url: str = "https://oapi.dingtalk.com/"
    clientId: str = Field(default=os.getenv("DINGTALK_CLIENT_ID", ""))
    clientSecret: str = Field(default=os.getenv("DINGTALK_CLIENT_SECRET", ""))
    DingLoginUrl: str = "https://login.dingtalk.com/oauth2/auth?redirect_uri={}&response_type=code&client_id={}&scope=openid&state=dddd&prompt=consent"
    
    @field_validator("clientId", "clientSecret")
    def validate_credentials(cls, v: str) -> str:
        if not v:
            raise ValueError("DingTalk credentials must be set in environment variables")
        return v

class UIConfig(BaseModel):
    """UI相关配置"""
    project_name: str = "约苗"
    global_color: str = "#0089ff"
    header_divider: str = "rainbow"
    rag_title: str = "{}智能问答助手"
    
    @property
    def rag_header(self) -> str:
        return f"🌈约苗<span style='color: {self.global_color};'>智能</span>问答助手🌟"
        
    @property
    def ym_link_style(self) -> str:
        return f"text-decoration: none;color: {self.global_color};"

    @property
    def get_page_config(self) -> Dict[str, Any]:
        return {
            "page_title": self.rag_title.format(self.project_name),
            "page_icon": os.path.join(PathConfig().ICON_PATH, "favicon.svg"),
            "layout": "wide",
            "initial_sidebar_state": "auto",
            "menu_items": None,
        }

    @property
    def get_logo_config(self) -> Dict[str, Any]:
        return {
            "image": os.path.join(PathConfig().ICON_PATH, "logo.png"),
            "link": "",
            "icon_image": "",
        }

class BusinessConfig(BaseModel):
    """业务相关配置"""
    MEMORY_KEY: str = "message_store"
    URL: str = "https://www.scmttec.com/"
    
    SECTION: Dict[str, str] = Field(default_factory=lambda: {
        "483622673": "总裁办",
        "898890885": "VIBS办公室",
        "892917224": "疫苗市场部",
        "916547197": "疫苗推广运营部",
        "916331437": "疫苗项目拓展部",
        "57005016": "应用拓展部",
        "662872054": "医院业务部",
        "662849063": "妇保业务部",
        "916715049": "平台运营部",
        "916652019": "产品部",
        "916462283": "研发部",
        "916612209": "品牌部",
        "916687033": "人力资源运营部",
        "36061503": "综合事务部",
        "916665033": "财务部",
        "916361414": "信息安全部",
        "937622173": "协作组",
    })

    Default_TOP: List[Tuple[str, int]] =  Field(default_factory=lambda: [
        ("接种卡相关问题有哪些？", 1),
        ("公众号无法访问如何处理？", 1),
        ("关于医生积分发放问题解答", 1),
        ("约苗管家及门诊相关问题？", 1)
    ])

    ROLES: Dict[str, str] = Field(default_factory=lambda: {
        "TSL": "🌈通识类知识",
        "TQ": "🦸技术知识",
        "PQ": "🤹产品知识",
        "CSQ": "🧖‍♀️业务知识",
        "AQ": "👩‍💼公司制度",
    })
    
    Q_STR: str = "您好呀！我是 :red[{}] 专属问答小助手，已深入学习公司业务、技术、产品以及各项规章制度等丰富内容，有任何问题都能尽管问我哟！"
    
    @property
    def Q_TEMPLATE(self) -> Dict[str, str]:
        return {
            "TQ": self.Q_STR.format("技术中心"),
            "PQ": self.Q_STR.format("产品中心"),
            "CSQ": self.Q_STR.format("业务中心"),
            "AQ": self.Q_STR.format("公司规章制度"),
            "TSL": self.Q_STR.format("通识中心"),
            "TY": self.Q_STR.format("约苗智能"),
        }

class LangfuseConfig(BaseModel):
    """Langfuse配置"""
    public_key: str = Field(default=os.getenv("LANGFUSE_PUBLIC_KEY"))
    secret_key: str = Field(default=os.getenv("LANGFUSE_SECRET_KEY"))
    host: str = Field(default=os.getenv("LANGFUSE_HOST"))

    @field_validator("public_key", "secret_key", "host")
    def validate_credentials(cls, v: str) -> str:
        if not v:
            raise ValueError("Langfuse credentials must be set in environment variables")
        return v
    
    def set_env_variables(self):
        """设置环境变量"""
        os.environ["LANGFUSE_PUBLIC_KEY"] = self.public_key
        os.environ["LANGFUSE_SECRET_KEY"] = self.secret_key
        os.environ["LANGFUSE_HOST"] = self.host

class LlmConfig(BaseModel):
    """LLM相关配置"""
    RETRIEVAL_LIST: List[str] = Field(default_factory=lambda: ["Base", "MultiQuery", "Compression", "Rerank"])
    MAX_TOKEN: int = 4096
    TEMPERATURE: float = 0.0


class PromptConfig(BaseModel):
    """Prompt相关配置"""
    rag: str = """
    你是约苗智能问答助手，专门根据提供的已知信息回答问题。请严格遵守以下规则：
    
    已知信息：{context}
    用户问题：{question}
    
    1. 回答格式要求：
       - 如果已知信息包含答案：以"同学，您好！很高兴给你解答\n"开头，并用有序列表回答
       - 如果已知信息不包含答案：必须且只能回复"知识库查询无相关知识，以下内容由模型为你继续回答！"，无需其他解释
    
    2. 回答准则：
       - 严格基于提供的已知信息回答用户问题
       - 禁止任何推测、联想或超出已知信息的回答
       - 当且仅当已知信息明确包含问题答案时才能回答
       - 如果答案不确定或信息不完整，视为不包含答案
    
    3. 附加要求：
       - 如果已知信息中标明了信息来源或者存在多个信息来源，请选择任意一个在回答末尾标注
       - 保持回答专业、详细、准确
    
    请现在回答用户问题，严格遵守上述所有要求。
    """

    router: str = """
    智能路由决策专家提示词

    执行三级路由判断规则：
    1. TQ技术问题匹配（优先级从高到低）：
       - 具体技术场景："API调用失败"、"SDK集成报错"、"数据库连接超时"
       - 技术组件："Docker"、"Kubernetes"、"MySQL"、"Redis"
       - 开发运维："部署"、"日志"、"监控"、"CI/CD"
    
    2. CSQ业务问题匹配（优先级从高到低）：
       - 核心业务场景："预约疫苗"、"接种疫苗"、"预约失败"、"订单异常"、"支付问题"、"疫苗库存"
       - 终端应用："小程序"、"公众号"、"约苗管家"、"门诊系统"、"Man端"、"市场端"、"运营端"、"疾控端"、"约苗助手"
       - 业务模块："疫苗约苗"、"两癌筛查"、"体检预约"、"合同管理"、"账单查询"
       - 其他服务："值班人员"
    
    3. AQ制度规范匹配（优先级从高到低）：
       - 人事制度："请假"、"报销"、"离职"、"绩效考核"
       - 行政流程："用章申请"、"合同审批"、"采购流程"
       - 合规要求："数据安全"、"保密协议"、"合规审计"
    
    复合匹配规则增强版：
    1. 优先匹配最长关键词组合（如"小程序登录失败"优于"登录失败"）
    2. 技术+业务交叉场景默认归为CSQ（如"API预约接口报错"→CSQ）
    3. 制度规范冲突时强制归为AQ（如"报销系统故障"→AQ）
    4. 新增模糊匹配兜底规则（未明确匹配时提示澄清问题）
    
    输出要求：
    - 严格按[TQ/CSQ/AQ]格式输出
    - 禁止任何解释性文字
    - 未知类型输出[UNKNOWN]
    
    示例场景：
    1. "文件上传失败"→CSQ
    2. "Docker如何部署"→TQ
    3. "我要报销"→AQ
    4. "服务器CPU报警"→TQ
    5. "预约页面白屏"→CSQ
    6. "页面提示无权限"→CSQ
    7. "今日值班人员是谁"→CSQ
    """

    search: str = """
    你是智能搜索助手，专门根据搜索结果回答问题。请严格遵守以下规则：
    
    搜索结果：{answer}
    用户问题：{question}

    1. 回答格式要求：
       - 如果搜索结果包含答案：以"以下结果均为网络搜索，由AI自动生成：\n"开头，并用有序列表简洁回答

    2. 回答准则：
       - 严格基于提供的搜索结果回答用户问题
       - 禁止任何推测、联想或超出搜索结果的回答
       - 当且仅当搜索结果明确包含问题答案时才能回答
       - 如果答案不确定或信息不完整，视为不包含答案
    
    3. 附加要求：
       - 如果搜索结果中标明了来源，请在回答末尾标注
       - 保持回答专业、简洁、准确
    
    请现在回答用户问题，严格遵守上述所有要求。
    """

    requirement: str = """
    作为一位专业的需求分析师，请对以下需求文档进行深入分析。请从以下几个维度进行详细评估：
    
    1. 需求概述
       - 项目背景和业务目标
       - 核心功能点梳理
       - 目标用户群体
    
    2. 需求完整性评估
       - 功能需求完整度
       - 非功能性需求覆盖情况（性能、安全、可用性等）
       - 是否存在需求遗漏或模糊点
    
    3. 需求质量分析
       - 需求的清晰度和明确性
       - 需求的可测试性
       - 需求间的一致性和冲突检查
       - 业务规则和约束条件的完整性
    
    4. 技术可行性评估
       - 技术实现难度
       - 系统架构建议
       - 潜在的技术风险点
    
    5. 项目风险评估
       - 识别关键风险点
       - 依赖关系分析
       - 可能的项目瓶颈
    
    6. 优化建议
       - 需求优化建议
       - 实现路径建议
       - 项目规划建议
    
    7. 补充说明
       - 需要进一步明确的问题
       - 建议补充的文档内容
    
    请基于以上维度，对下面的需求文档进行分析：
    
    {question}
    
    请以结构化的方式输出分析结果，并特别标注出重要发现和关键风险。
    """

    technical: str = """
    作为资深技术架构师，请对以下技术文档进行全面评审。请从以下维度进行专业评估：
    
    1. 技术架构概述
       - 设计背景与技术目标
       - 核心组件/模块划分
       - 采用的技术栈与工具链
    
    2. 文档完整性评估
       - 系统模块描述完整性
       - 接口定义与协议规范
       - 数据模型与流程覆盖度
       - 部署架构与运维方案
       - 关键技术决策说明
    
    3. 技术质量分析
       - 架构设计的合理性评估
       - 系统的可扩展性设计
       - 高可用与容灾机制
       - 安全防护体系完备性
       - 与行业标准的符合度
    
    4. 实现可行性评估
       - 技术选型的成熟度评估
       - 系统组件兼容性分析
       - 核心技术实现路径验证
       - 资源需求（计算/存储/网络）
    
    5. 技术风险评估
       - 识别架构单点故障
       - 性能瓶颈预测
       - 技术债务评估
       - 第三方依赖风险
       - 安全漏洞潜在点
    
    6. 优化建议
       - 架构设计改进建议
       - 性能优化方案
       - 代码规范加强方向
       - 自动化工具链建议
       - 技术债务偿还方案
    
    7. 补充说明
       - 需要澄清的技术实现细节
       - 建议补充的技术文档内容
       - 推荐的技术验证方案
    
    请基于以上维度，对以下技术文档进行分析：
    {question}
    
    输出要求：
    1. 以分级目录形式结构化呈现
    2. 关键技术缺陷用【⚠️警告】标注
    3. 优秀设计采用【✅推荐】标注
    4. 核心建议用⭐⭐⭐特殊标记
    """

    general: str = """
    作为专业文档分析师，请对以下文档进行结构化评估。请按以下维度展开分析：
    
    1. 文档宏观评估
       - 文档类型与行业领域识别
       - 核心目标与预期价值
       - 目标受众与使用场景分析
    
    2. 内容完整性分析
       - 关键要素覆盖度检查（根据文档类型自动适配）
       - 数据/事实的准确性与时效性
       - 必要附录与支撑材料完整性
       - 法律/合规性要件核查（如涉及）
    
    3. 逻辑结构评估
       - 信息层级架构合理性
       - 内容模块衔接流畅度
       - 重点突出与分层展示效果
       - 可视化元素的恰当性（图表/示意图等）
    
    4. 专业深度评估
       - 专业术语使用规范性
       - 技术/业务逻辑严谨性
       - 创新价值与行业前瞻性
       - 风险预测的全面性
    
    5. 实践可行性分析
       - 实施路径的可操作性
       - 资源配置合理性评估
       - 时间线规划的现实性
       - 利益相关方覆盖全面性
    
    6. 优化改进建议
       - 内容层面的完善建议
       - 结构表达优化方案 
       - 风险控制加强措施
       - 价值提升创新方向
    
    7. 增强建议
       - 建议补充的支撑材料
       - 推荐的关键指标验证方法
       - 可引入的分析工具/框架建议
    
    请对以下文档进行分析：
    {question}
    
    输出要求：
    • 采用三级目录结构呈现
    • 遵循「问题描述-分析过程-改进建议」框架
    • 关键缺陷使用【🔴警报】标记
    • 优秀实践应用【🟢最佳】标注
    • 战略性建议前添加✨符号
    """

    chat: str = """
    基于以下文档内容，回答用户的问题。
    
    文档内容：
    {doc_content}
    
    用户问题：
    {question}
    
    特别要求：
    1. 请客观公正的阐述自己观点。
    2. 只需要回复用户问题，无需回复相关解释或说明，否则将受到非常严厉的处罚！
    """

    testcase: str = """
    Role: 资深软件测试工程师
    Task: 根据提供的功能需求文档，设计一套结构清晰、覆盖全面、易于执行的手工测试用例集合。

    【第一步：需求复杂度评估】
    请首先对需求文档进行复杂度评估，并根据以下维度计算建议的测试用例数量：

    复杂度评估维度：
    1. 输入字段数量：每个输入字段 +2 条用例（正例+异常）
    2. 业务流程分支：每个分支路径 +3 条用例
    3. 状态转换：每个状态变化 +2 条用例
    4. 权限角色：每个角色 +3 条用例
    5. 跨模块交互：每个交互点 +2 条用例
    6. 数据验证规则：每个验证规则 +2 条用例
    7. 异常处理场景：每个异常类型 +1 条用例

    复杂度等级判定：
    - 简单需求（总分≤20）：生成 12-18 条用例
    - 中等复杂度（总分21-40）：生成 18-30 条用例
    - 复杂需求（总分41-60）：生成 30-45 条用例
    - 高复杂度（总分>60）：生成 45-60 条用例

    【第二步：输入分析】
    请对用户提供的需求文档进行详细分析和提取：
    1. 功能模块名称及核心流程；
    2. 所有涉及的输入字段及其限制（如类型、长度、必填、格式）；
    3. 系统状态变化和可能的分支逻辑；
    4. 排序、筛选、展示等附加规则（如有）；
    5. 异常处理逻辑和错误提示要求；
    6. 是否存在权限、数据一致性、跨模块交互等复杂场景。

    【第三步：用例生成要求】
    请基于复杂度评估结果，生成相应数量的测试用例，并满足以下要求：
    - 覆盖正向流程、异常操作、边界值、组合条件；
    - 边界值包括最小值、最大值、空值、特殊字符、非法格式；
    - 每条用例应包含用例编号、用例标题、模块、优先级、前置条件、输入数据、操作步骤、预期结果；
    - 编写风格应清晰、简洁、可执行，结果可验证；
    - 优先级分为 P0（阻断）、P1（核心）、P2（重要）、P3（次要）；
    - 确保用例数量与需求复杂度相匹配，复杂需求必须生成更多用例；
    - 若无法完成某部分，请返回尽可能详细的占位用例并标注问题点。

    【输出格式】
    请以 JSON 格式输出测试用例数组，示例如下：

    [
      {{
        "用例编号": "LOGIN-AUTH-001",
        "用例标题": "用户登录-使用合法用户名和密码登录 [正例]",
        "模块": "登录认证",
        "优先级": "P0",
        "前置条件": ["系统运行正常", "用户已注册"],
        "测试数据": ["用户名=admin", "密码=Admin@123"],
        "操作步骤": [
          "1.访问登录页面",
          "2.输入用户名 'admin'",
          "3.输入密码 'Admin@123'",
          "4.点击登录按钮"
        ],
        "预期结果": [
          "1.登录成功",
          "2.页面跳转至首页",
          "3.显示欢迎消息及用户信息"
        ]
      }}
    ]

    【特别注意】
    1. 请仅输出符合格式的 JSON 数组，不要添加任何解释、说明或备注。
    2. 必须根据需求复杂度生成相应数量的用例，不能固定生成10条。
    3. 复杂需求必须生成更多用例以确保充分覆盖。
    """

    api_prompt: str = """
    你是愿望分析助手，你的职责是通过分析用户问题中提取关键信息。

    用户问题：{user_question}
    
    字段解析：
    - "who"从用户问题中提取与本人的关系
    - "job"从用户问题中提取职业信息
    - "to_do"从用户问题中提取他的愿望内容
    
    输出示例：
    {{
      "who": "我",
      "job": "程序员",
      "to_do": "代码写的越来越好"
    }}
    
    特别指令:
    1. 严格按照输出示例格式输出JSON结果
    
    请根据以上指南，生成相关内容。
    """

    demand_analysis: str = """
    Role: 资深软件测试工程师  
    Task: 根据提供的需求文档，提取尽可能全面的测试点并进行风险分析。

    请根据以下规则进行处理：

    1. 仔细阅读需求文档，识别其是否包含与用户界面相关的功能（如页面展示、前端交互、UI元素控制等）。
    2. 如果需求中**未提及 UI 相关内容**，则**不要列出“UI测试”类别下的测试点**。
    3. 对于每个测试点：
        - 自动分配唯一编号，格式为 TP001, TP002 等；
    4. 分类包括：
        - 功能测试
        - UI测试
        - 性能测试
        - 安全测试
        - 兼容性测试
        - 风险与需求分析
    
    对于每个测试点，请输出以下字段：
    - 编号（自动生成，格式为TP001, TP002...）
    - 分类
    - 子类
    - 优先级（高/中/低）
    - 测试点（具体测试点内容）
    - 风险等级（高/中/低）
    - 影响范围（仅当风险等级为“中”或“高”时填写）
    
    Output Format:
    - 输出一个标准 JSON 数组；
    - 每个对象包含上述七个字段；
    - 使用中文字段名；
    - 不添加任何解释、说明或备注；
    - 输出必须严格符合 JSON 格式，使用英文双引号；
    - 如果有中文内容，请确保使用 UTF-8 编码。
    
    示例格式如下（仅供参考）：
    [
      {{
        "分类": "安全测试",
        "子类": "权限控制",
        "优先级": "高",
        "测试点": "验证普通用户无法访问管理员页面",
        "风险等级": "高",
        "影响范围": "存在越权访问风险，可能导致数据泄露或误操作"
      }}
    ]
    """


class Settings(BaseSettings):
    """主配置类"""
    version: str = "V2.0"
    env: str = Field(default=os.getenv("ENV"))
    
    # 子配置
    paths: PathConfig = Field(default_factory=PathConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    dingtalk: DingTalkConfig = Field(default_factory=DingTalkConfig)
    ui: UIConfig = Field(default_factory=UIConfig)
    business: BusinessConfig = Field(default_factory=BusinessConfig)
    langfuse: LangfuseConfig = Field(default_factory=LangfuseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    llm: LlmConfig = Field(default_factory=LlmConfig)
    prompt: PromptConfig = Field(default_factory=PromptConfig)

    # 默认超时时间1天
    TIME_OUT: int = 86400
    MAX_SIZE: int = 100
    CUDA_ENABLED:  bool = False

    @field_validator("env")
    def validate_env(cls, v: str) -> str:
        allowed = {"dev", "prod"}
        if v not in allowed:
            raise ValueError(f"env must be one of {allowed}")
        return v
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.database = DatabaseConfig(env=self.env)
        self.langfuse.set_env_variables()
        self.api = APIConfig(env=self.env)

@lru_cache(maxsize=100)
def get_settings() -> Settings:
    """获取配置单例"""
    return Settings()


settings = get_settings()
