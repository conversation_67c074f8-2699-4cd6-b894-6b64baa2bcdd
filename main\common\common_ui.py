#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: common_ui.py 
@time: 2025/8/6 13:58
@Description: 
"""
from common.utils import safe_markdown, Year
from const.style import centered_css

def content_ui(settings, URL: str = None, is_login: bool = True) -> None:
    """内容UI样式"""
    safe_markdown(centered_css, allow_html=True)

    # 构建基础HTML内容
    html_content = f"""
            <div class="body-container">
                <div>
                    <h1 style="font-weight: 700; color: #1e293b; margin-bottom: 20px; line-height: 1.2;">
                        {settings.ui.rag_header}
                    </h1>"""

    # 根据is_login参数决定是否添加描述和按钮
    if is_login:
        html_content += f"""
                    <h6 style="font-size: 16px; color: #64748b; margin-bottom: 20px; line-height: 1.6; font-weight: 400;">
                        融合约苗业务知识，基于AI大模型技术，为您提供专业精准的智能问答服务
                    </h6>
                    <a href="{URL}" target="_self" class="bt-link">开始使用</a>"""

    html_content += """
                </div>
            </div>"""

    safe_markdown(html_content, allow_html=True)


def footer_ui(settings) -> None:
    """底部UI样式"""
    safe_markdown(f"""
        <div class="main-content">
            <!-- 主内容区域已完成 -->
        </div>

        <div class="footer-fixed">
            <div style="color: #64748b; font-size: 13px; line-height: 1.5; max-width: 600px; margin: 0 auto;">
                <div style="margin-bottom: 8px;">
                    © {Year} <a href="{settings.business.URL}" style="color: #0089ff; text-decoration: none; font-weight: 500;">约苗</a>
                </div>
                <div style="font-size: 12px; color: #94a3b8;">
                    🌟 让AI更好地服务于您 · 智能问答新体验
                </div>
            </div>
        </div>
        """, allow_html=True)