#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: base_generator.py
@time: 2025/4/15 10:00
@Description: 生成器基类
"""
from typing import Dict, AsyncIterator, Any

import streamlit as st
from langchain_core.prompts import ChatPromptTemplate
from langfuse.langchain import CallbackHandler

from common.myLog import logger
from core.chain import ChainFactory
from core.prompt import prompt_manager


class BaseGenerator:
    """生成器基类"""
    
    def __init__(
            self,
            prompt_name: str,
            parser: Any = None
    ) -> None:
        """初始化生成器基类
        
        Args:
            prompt_name: 提示词模板名称
        """
        self.prompt_name = prompt_name
        self.parser = parser
        self.chain_factory = ChainFactory()
        
    def _init_chain(self):
        """初始化Chain模型"""
        try:
            return self.chain_factory.create_chain(
                chain_type="common",
                prompt=self._init_prompt(),
                parser=self.parser,
                session_state=st.session_state
            )
        except Exception as e:
            logger.error(f"初始化Chain失败: {str(e)}")
            raise
            
    def _init_prompt(self) -> ChatPromptTemplate:
        """初始化提示词模板"""
        try:
            return prompt_manager.chat_template(
                name=self.prompt_name,
                human_message="原始需求：{question}"
            )
        except Exception as e:
            logger.error(f"初始化提示词模板失败: {str(e)}")
            raise
            
    async def generate_content(
            self,
            question: Dict[str, str],
            user_id: str
    ) -> AsyncIterator[str]:
        """异步生成内容
        
        Args:
            question: 问题参数
            user_id: 用户ID
            
        Yields:
            生成的内容片段
        """
        try:
            async for chunk in self._init_chain().astream_events(
                    question,
                    version="v2",
                    config={
                        "callbacks": [CallbackHandler()],
                        "metadata": {
                            "langfuse_user_id": user_id,
                        }
                    }
            ):
                if chunk['event'] == "on_chat_model_stream":
                    yield chunk["data"]['chunk'].content
        except Exception as e:
            logger.error(f"生成内容失败: {str(e)}")
            raise 