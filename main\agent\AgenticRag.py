#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: langgraphAgent.py
@time: 2025/2/7 10:37
@Description: 
"""
import re
import asyncio
import time
from typing import Dict, TypedDict, Any, Optional

from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.checkpoint.memory import MemorySaver
from langfuse.callback import Callback<PERSON>andler

from common.myLog import logger
from core.chain import ChainFactory
from core.prompt import prompt_manager
from core.tools import TavilySearchResultsTool
from const.cache import cache
from common.cache_manager import AdvancedCacheManager, global_cache
from config.streaming_config import StreamingConfig, StreamingOptimizer, BALANCED_STREAMING

memory = MemorySaver()

logger.name = __name__

# 全局正则表达式
ANSWER_PATTERN = re.compile(r"未学习|抱歉|对不起|不知道|无法回答|无相关")


class ChatState(TypedDict):
    """对话状态管理"""
    question: str
    answer: str
    target_collection: str
    needs_search: bool


class WorkflowEngine:
    """智能工作流引擎"""
    _cache_manager = global_cache

    def __init__(
            self,
            session_state: Dict[str, Any] = None,
            thread_id: str = None
    ) -> None:
        self.session_state = session_state
        self.thread_id = thread_id
        self.chain_factory = ChainFactory()

        try:
            # 在初始化时创建所需的 chains
            logger.info("开始创建路由 chain...")
            self.router_chain = self._get_router_chain()

            self.workflow = self._build_workflow()
        except Exception as e:
            logger.error(f"WorkflowEngine 初始化失败: {str(e)}", exc_info=True)
            raise

    def _get_router_chain(self):
        """获取路由链（使用高级缓存管理器）"""
        cache_key = f"router_{self.thread_id}"

        def create_router_chain():
            logger.info(f"创建新的路由 chain: {cache_key}")
            return self.chain_factory.create_chain(
                chain_type="router",
                session_state=self.session_state
            )

        return self._cache_manager.get_or_create(
            cache_key,
            create_router_chain,
            ttl=1800  # 30分钟过期
        )

    def _get_rag_chain(self, collection_name):
        """获取RAG链（使用高级缓存管理器）"""
        cache_key = f"rag_{collection_name}_{self.thread_id}"

        def create_rag_chain():
            logger.info(f"开始创建 RAG chain: {collection_name}")
            chain = self.chain_factory.create_chain(
                chain_type="rag",
                retriever_type="Rerank",
                collection_name=collection_name,
                session_state=self.session_state
            )
            logger.info(f"RAG chain 创建成功: {collection_name}")
            return chain

        return self._cache_manager.get_or_create(
            cache_key,
            create_rag_chain,
            ttl=3600  # 1小时过期
        )

    def _get_common_chain(self):
        """获取通用链（使用高级缓存管理器）"""
        cache_key = f"common_{self.thread_id}"

        def create_common_chain():
            logger.info(f"创建新的通用 chain: {cache_key}")
            return self.chain_factory.create_chain(
                chain_type="common",
                prompt=prompt_manager.utils_prompt("search_prompt"),
                session_state=self.session_state
            )

        return self._cache_manager.get_or_create(
            cache_key,
            create_common_chain,
            ttl=1800  # 30分钟过期
        )

    def _build_workflow(self) -> CompiledStateGraph:
        builder = StateGraph(ChatState)

        async def route_question(state: ChatState) -> Dict:
            logger.info(f"路由问题: {state['question']}")
            try:
                answer = await self.router_chain.ainvoke(
                    {
                        "question": state["question"]
                    }
                )
                cache.put(f"target_collection_{self.thread_id}", answer)
                logger.info(f"路由结果: {answer}")
                return {"target_collection": answer}
            
            except Exception as e:
                logger.error(f"路由错误: {str(e)}", exc_info=True)
                raise RuntimeError(f"路由处理失败: {str(e)}")

        async def retrieve_answer(state: ChatState) -> Dict:
            try:
                # 为每个请求创建新的 processor chain
                processor = self._get_rag_chain(state["target_collection"])
                answer = await processor.ainvoke(
                    {"question": state["question"]}
                )
                return {"answer": answer}
            except Exception as e:
                logger.error(f"检索器初始化失败: {str(e)}", exc_info=True)
                raise

        def validate_answer(state: ChatState) -> Dict:
            if ANSWER_PATTERN.search(state['answer']):
                logger.info("未学习，需要网络搜索")
                return {"answer": state['answer'], "needs_search": True}
            else:
                logger.info("无需网络搜索")
                return {"answer": state["answer"], "needs_search": False}

        async def perform_search(state: ChatState) -> Dict:
            try:
                loop = asyncio.get_event_loop()
                SearchResults = await loop.run_in_executor(
                    None,
                    TavilySearchResultsTool,
                    state["question"],
                    5
                )

                common_chain = self._get_common_chain()

                # 检查是否有 invoke 方法
                if not hasattr(common_chain, "invoke"):
                    raise AttributeError("common_chain 没有 invoke 方法")

                rp = await common_chain.ainvoke(
                    {
                        "question": state["question"],
                        "answer": SearchResults
                    }
                )

                return {"answer": f"{state['answer']}\n\n网络搜索结果：\n{rp}"}
            except Exception as e:
                logger.error(f"网络搜索错误: {str(e)}")
                raise

        # 注册节点
        nodes = {
            "route": route_question,
            "retrieve": retrieve_answer,
            "validate": validate_answer,
            "search": perform_search
        }
        for name, node in nodes.items():
            builder.add_node(name, node)

        # 修改流程构建
        builder.add_edge(START, "route")
        builder.add_edge("route", "retrieve")
        builder.add_edge("retrieve", "validate")

        def DecisionRouting(state: ChatState):
            if not state.get("needs_search", False):
                return "END"
            else:
                return "search"

        builder.add_conditional_edges(
            'validate', DecisionRouting,
            {
                "END": END,
                "search": "search"
            }
        )

        builder.add_edge("search", END)

        datas = builder.compile(checkpointer=memory)

        return datas

    async def astream(self, question: str):
        """异步流式输出 - 优化版本"""
        config = {
            "configurable": {
                "thread_id": self.thread_id,
                "streaming": True
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "langfuse_user_id": self.thread_id,
            }
        }

        # 预先获取target_collection，避免重复查询
        target_collection = None
        chain_end_handled = False

        # 定义需要处理的节点，提高过滤效率
        target_nodes = {"retrieve", "search"}

        try:
            # 使用更高效的流式模式
            async for event in self.workflow.astream_events(
                    {"question": question},
                    config,
                    version="v2",
                    stream_mode="updates"  # 改为updates模式，更高效
            ):
                event_type = event.get("event")

                # 优化：只在需要时获取target_collection
                if target_collection is None:
                    target_collection = cache.get(f"target_collection_{self.thread_id}")

                # 优化：使用更高效的事件过滤
                if event_type == "on_chat_model_stream":
                    node_name = event.get('metadata', {}).get('langgraph_node', '')
                    if node_name in target_nodes:
                        chunk_content = event.get("data", {}).get('chunk', {})
                        if hasattr(chunk_content, 'content'):
                            yield chunk_content.content, target_collection
                            chain_end_handled = True

                elif event_type == "on_chain_end" and event.get('name') == "LangGraph":
                    if not chain_end_handled:
                        output_data = event.get("data", {}).get('output', {})
                        answer = output_data.get('answer', '')
                        if answer:
                            yield answer, target_collection

        except Exception as e:
            logger.error(f"流式输出错误: {e}", exc_info=True)
            raise

    async def astream_optimized(self, question: str, buffer_size: int = 5):
        """高度优化的异步流式输出 - 使用缓冲机制"""
        config = {
            "configurable": {
                "thread_id": self.thread_id,
                "streaming": True
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "langfuse_user_id": self.thread_id,
            }
        }

        # 使用缓冲区提高输出效率
        buffer = []
        target_collection = None
        chain_end_handled = False

        # 预编译正则表达式提高性能
        target_nodes = {"retrieve", "search"}

        async def flush_buffer():
            """刷新缓冲区"""
            if buffer:
                content = "".join(buffer)
                buffer.clear()
                return content
            return ""

        try:
            async for event in self.workflow.astream_events(
                    {"question": question},
                    config,
                    version="v2",
                    stream_mode="updates"
            ):
                # 延迟获取target_collection
                if target_collection is None:
                    target_collection = cache.get(f"target_collection_{self.thread_id}")

                event_type = event.get("event")

                if event_type == "on_chat_model_stream":
                    node_name = event.get('metadata', {}).get('langgraph_node', '')
                    if node_name in target_nodes:
                        chunk_content = event.get("data", {}).get('chunk', {})
                        if hasattr(chunk_content, 'content') and chunk_content.content:
                            buffer.append(chunk_content.content)
                            chain_end_handled = True

                            # 当缓冲区达到指定大小时输出
                            if len(buffer) >= buffer_size:
                                content = await flush_buffer()
                                if content:
                                    yield content, target_collection

                elif event_type == "on_chain_end" and event.get('name') == "LangGraph":
                    # 输出剩余缓冲区内容
                    content = await flush_buffer()
                    if content:
                        yield content, target_collection

                    # 如果没有流式内容，输出完整答案
                    if not chain_end_handled:
                        output_data = event.get("data", {}).get('output', {})
                        answer = output_data.get('answer', '')
                        if answer:
                            yield answer, target_collection

            # 确保所有缓冲内容都被输出
            final_content = await flush_buffer()
            if final_content:
                yield final_content, target_collection

        except Exception as e:
            logger.error(f"优化流式输出错误: {e}", exc_info=True)
            raise

    async def astream_high_performance(
        self,
        question: str,
        config: Optional[StreamingConfig] = None
    ):
        """高性能流式输出 - 使用配置化优化"""
        streaming_config = config or BALANCED_STREAMING
        optimizer = StreamingOptimizer(streaming_config)

        workflow_config = {
            "configurable": {
                "thread_id": self.thread_id,
                "streaming": True
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "langfuse_user_id": self.thread_id,
            }
        }

        # 性能优化变量
        buffer = []
        current_node = None
        target_collection = None
        chain_end_handled = False
        last_flush_time = time.time()

        async def smart_flush():
            """智能刷新缓冲区"""
            nonlocal last_flush_time
            if buffer:
                content = "".join(buffer)
                buffer.clear()
                last_flush_time = time.time()
                return content
            return ""

        try:
            async for event in self.workflow.astream_events(
                    {"question": question},
                    workflow_config,
                    version=streaming_config.version,
                    stream_mode=streaming_config.stream_mode
            ):
                # 延迟获取target_collection（缓存优化）
                if target_collection is None and streaming_config.enable_caching:
                    target_collection = cache.get(f"target_collection_{self.thread_id}")

                event_type = event.get("event")
                event_name = event.get("name", "")
                icon = "🔹"

                if event_type == "on_chain_start" and event_name in streaming_config.event_names:
                    current_node = event_name
                    node_descriptions = {
                        "route": "正在分析问题类型并选择知识库",
                        "retrieve": "正在从知识库中检索相关信息",
                        "search": "正在互联网上搜索补充信息"
                    }
                    start_node_message = f"\n\n{icon} {node_descriptions.get(current_node, '正在处理')}...\n\n"
                    buffer.append(start_node_message)

                    flushed_content = await smart_flush()
                    if flushed_content:
                        yield start_node_message, target_collection

                # 节点完成事件
                elif event_type == "on_chain_end" and event_name in streaming_config.event_names:
                    if current_node == event_name:
                        output = event.get("data", {}).get("output", {})
                        route_knowledge = {
                            "CSQ": "业务知识库 ",
                            "AQ": "公司制度知识库 ",
                            "TQ": "技术知识库 "
                        }
                        if event_name == "route":
                            target = output.get("target_collection", "未知")
                            knowledge_base = route_knowledge.get(target, '未知知识库')
                            end_node_message = f"\n\n{icon} 问题分析完成，将使用: {knowledge_base}\n\n"
                            buffer.append(end_node_message)

                            flushed_content = await smart_flush()
                            if flushed_content:
                                yield end_node_message, target_collection

                    else:
                        logger.warning(f"当前节点: {current_node}, 忽略事件: {event}")

                elif event_type == "on_chat_model_stream":
                    node_name = event.get('metadata', {}).get('langgraph_node', '')
                    if node_name in streaming_config.target_nodes:
                        chunk_content = event.get("data", {}).get('chunk', {})
                        if hasattr(chunk_content, 'content') and chunk_content.content:
                            content = chunk_content.content
                            buffer.append(content)
                            chain_end_handled = True

                            # 更新性能指标
                            if streaming_config.enable_metrics:
                                optimizer.update_metrics(len(content), 0.001)

                            # 智能缓冲区管理
                            if optimizer.should_flush_buffer(len(buffer), last_flush_time):
                                flushed_content = await smart_flush()
                                if flushed_content:
                                    yield flushed_content, target_collection

                elif event_type == "on_chain_end" and event_name == "LangGraph":
                    # 输出剩余缓冲区内容
                    flushed_content = await smart_flush()
                    if flushed_content:
                        yield flushed_content, target_collection

                    # 处理非流式内容
                    if not chain_end_handled:
                        output_data = event.get("data", {}).get('output', {})
                        answer = output_data.get('answer', '')
                        if answer:
                            yield answer, target_collection

            # 确保所有内容都被输出
            final_content = await smart_flush()
            if final_content:
                yield final_content, target_collection

        except Exception as e:
            logger.error(f"高性能流式输出错误: {e}", exc_info=True)
            raise

if __name__ == '__main__':
    ...