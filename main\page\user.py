#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: user.py
@time: 2024/9/29 15:07
@Description:
"""
from typing import List, Dict, Any
from collections import OrderedDict
from dataclasses import dataclass
import asyncio

import pandas as pd
import streamlit as st
from pydantic import ValidationError

from config.config_center import settings
from common.myLog import logger
from common.utils import SerializedData
from const.constants import (
    UpdateUser, UpdateRole, UpdateConfig,
    USER_TABLE_COLUMNS, ROLE_TABLE_COLUMNS,
    AUTH_TABLE_COLUMNS, CONFIG_TABLE_COLUMNS
)
from core.prompt import prompt_manager
from page.cache_management import init_cache_management


logger.name = __name__

# 类型定义
DataFrameType = pd.DataFrame
JsonDict = Dict[str, Any]

@dataclass
class TableConfig:
    """表格配置数据类"""
    columns: Dict[str, Any]
    disabled_fields: List[str]
    column_order: List[str]

# 缓存配置
CACHE_TTL = 3600

@st.cache_data(ttl=CACHE_TTL)
def get_auth_list() -> List[JsonDict]:
    """获取权限列表(带缓存)"""
    try:
        return st.session_state.curd.get_auth
    except Exception as e:
        logger.error(f"获取权限列表失败: {str(e)}")
        return []

def to_ordered_dict(data: Dict, column_order: List[str]) -> OrderedDict:
    """将字典转换为有序字典"""
    return OrderedDict((k, data[k]) for k in column_order if k in data)

class DataEditor:
    """数据编辑器基类"""
    def __init__(self, data: List[JsonDict], table_config: TableConfig):
        self.data = data
        self.config = table_config
        self.df = pd.DataFrame(data)

    def edit(self) -> DataFrameType:
        """显示数据编辑器"""
        return st.data_editor(
            self.df,
            column_order=self.config.column_order,
            column_config=self.config.columns,
            disabled=self.config.disabled_fields,
            hide_index=True,
            use_container_width=True
        )

def save_edited_row(
    before_data: DataFrameType,
    after_data: DataFrameType,
    update_function: callable,
    **kwargs
) -> None:
    try:
        if before_data.equals(after_data):
            return

        if st.button("保存修改", help="保存修改后的信息", type="primary"):
            edited_rows = after_data[~after_data.eq(before_data).all(axis=1)]
            edited_content = []
            
            for row in edited_rows.to_dict(orient='records'):
                entry = {
                    field: row.get(field)
                    for field in kwargs
                }
                edited_content.append(entry)

            for content in edited_content:
                update_function(**content)
            
            st.success("保存成功")
            st.rerun()

    except ValidationError as e:
        st.error(f"数据验证失败: {str(e)}")
    except Exception as e:
        logger.error(f"保存数据失败: {str(e)}")
        st.error(f"保存失败: {str(e)}")

@st.dialog("添加角色")
def add_role() -> None:
    """添加角色对话框"""
    try:
        rule_name = st.text_input("角色名称", max_chars=50)
        auth_data = SerializedData(st.session_state.curd.get_auth)
        
        auth_options = {
            item['auth_name']: item['id'] 
            for item in auth_data
        }
        
        selected_options = st.multiselect(
            "选择权限",
            options=list(auth_options.keys())
        )
        
        selected_ids = [
            str(auth_options[option])
            for option in selected_options
        ]
        
        remark = st.text_area("备注", max_chars=200)
        
        if st.button(
            "保存",
            disabled=(not rule_name or not selected_ids)
        ):
            st.session_state.curd.add_role(
                auth_id=','.join(selected_ids),
                role_name=rule_name,
                remark=remark
            )
            st.success("添加成功")
            st.rerun()

    except Exception as e:
        logger.error(f"添加角色失败: {str(e)}")
        st.error(f"添加失败: {str(e)}")

@st.dialog("添加配置")
def add_config() -> None:
    """添加配置对话框"""
    try:
        config_name = st.text_input("配置名称", max_chars=50)
        config_value = st.text_area("配置内容")
        remark = st.text_area("备注", max_chars=200)
        
        if st.button(
            "保存",
            disabled=(not config_name or not config_value)
        ):
            st.session_state.curd.add_config(
                config_name=config_name,
                config_value=config_value,
                create_name=st.session_state.userInfo.get("dt_name", ""),
                remark=remark
            )
            st.success("添加成功")
            st.rerun()

    except Exception as e:
        logger.error(f"添加配置失败: {str(e)}")
        st.error(f"添加失败: {str(e)}")

async def init_user() -> None:
    """初始化系统管理界面"""
    st.header("系统管理", divider=settings.ui.header_divider)

    tabs = st.tabs(["用户管理", "角色管理", "权限管理", "基础配置", "缓存管理"])

    # 用户管理
    with tabs[0]:
        user_config = TableConfig(
            columns=USER_TABLE_COLUMNS | {
                "roles_id": st.column_config.SelectboxColumn(
                    "角色ID",
                    options=st.session_state.curd.get_role_ids,
                    required=True,
                )
            },
            disabled_fields=[
                "create_time", "modify_time", "dt_unionid",
                "dt_userid", "dt_name", "id", "dt_title", "dt_mobile"
            ],
            column_order=list(USER_TABLE_COLUMNS.keys())
        )
        
        user_editor = DataEditor(
            SerializedData(st.session_state.curd.get_user),
            user_config,
        )
        edited_data = user_editor.edit()
        
        save_edited_row(
            user_editor.df,
            edited_data,
            st.session_state.curd.update_user,
            **UpdateUser().dict()
        )

    # 角色管理
    with tabs[1]:
        if st.button("添加角色"):
            add_role()
            
        role_config = TableConfig(
            columns=ROLE_TABLE_COLUMNS,
            disabled_fields=["id", "create_time", "modify_time", "yn"],
            column_order=list(ROLE_TABLE_COLUMNS.keys())
        )
        
        role_editor = DataEditor(
            SerializedData(st.session_state.curd.get_role),
            role_config
        )
        edited_role = role_editor.edit()
        
        save_edited_row(
            role_editor.df,
            edited_role,
            st.session_state.curd.update_role,
            **UpdateRole().dict()
        )

    # 权限管理
    with tabs[2]:
        auth_config = TableConfig(
            columns=AUTH_TABLE_COLUMNS,
            disabled_fields=[],
            column_order=list(AUTH_TABLE_COLUMNS.keys())
        )
        
        auth_editor = DataEditor(
            SerializedData(st.session_state.curd.get_auth),
            auth_config
        )
        auth_editor.edit()

    # 基础配置
    with tabs[3]:

        if st.button("添加配置"):
            add_config()

        config_config = TableConfig(
            columns=CONFIG_TABLE_COLUMNS,
            disabled_fields=[
                "id", "config_name", "create_time",
                "modify_time", "create_name", "yn"
            ],
            column_order=list(CONFIG_TABLE_COLUMNS.keys())
        )
        
        config_editor = DataEditor(
            SerializedData(st.session_state.curd.get_config),
            config_config
        )
        edited_config = config_editor.edit()
        
        save_edited_row(
            config_editor.df,
            edited_config,
            st.session_state.curd.update_config,
            **UpdateConfig().dict()
        )

    # 缓存管理
    with tabs[4]:
        await init_cache_management()