#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: app.py
@time: 2024/9/29 15:07
@Description:
"""
import time
from typing import Dict, List
from dataclasses import dataclass

import streamlit as st
import streamlit_antd_components as sac

from common.myLog import logger
from config.config_center import settings
from common.initialize import init_data, Login, init_llm, init_curd
from common.utils import Year, get_time_period, error_handler, with_loading, safe_markdown
from const.enums import SystemMenuEnum
from const.style import centered_css
from const.menu_mapping import handle_menu



@dataclass
class AppConfig:
    """应用配置类"""
    page_config = settings.ui.get_page_config
    logo_config = settings.ui.get_logo_config
    login_url = settings.dingtalk.DingLoginUrl
    version = settings.version


class AuthManager:
    """用户认证管理类"""
    @staticmethod
    def check_auth(user_info: Dict) -> bool:
        return any([
            user_info.get("is_admin"),
            user_info.get("is_super")
        ])

    @staticmethod
    def get_user_menu(auth_ids: List[int], is_auth: bool) -> List:
        menu_system = SystemMenuEnum(auth_ids, is_auth)
        return [menu_system().get(i) for i in auth_ids if i in menu_system()]

def check_session_expired():
    """检查会话是否过期"""
    if 'last_activity' in st.session_state:
        if time.time() - st.session_state.last_activity > 7200:
            st.session_state.clear()
            st.info("会话已过期，请重新登录")
            st.stop()
    st.session_state.last_activity = time.time()


def initialize_basic_config():
    """初始化基础配置"""
    app_config = AppConfig()
    st.set_page_config(**app_config.page_config)
    st.logo(**app_config.logo_config)
    return app_config


def initialize_session_data():
    """初始化会话数据"""
    if "session_data_initialized" not in st.session_state:
        init_data()
        st.session_state.session_data_initialized = True


def render_login_page(app_config: AppConfig):
    """渲染登录页面"""
    safe_markdown(centered_css, allow_html=True)
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)
    safe_markdown(f"""
    <div class="body-container">
        <div>
            <h1>{settings.ui.rag_header}</h1>
            <a href="{URL}" target="_self" class="bt-link">开始使用</a>
        </div>
    </div>
    """, allow_html=True)
    safe_markdown(f"""
    <div class="ym-container">
        <span>© {Year} <a href="{settings.business.URL}" style="{settings.ui.ym_link_style}">约苗</a></span>
    </div>
    """, allow_html=True)


def render_login_page_new(app_config: AppConfig):
    """渲染新版登录页面 - 更美观的智能问答系统登录界面"""
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)

    # 创建全屏布局容器
    st.markdown("""
    <div style="min-height: 100vh; display: flex; flex-direction: column;
                margin: -1rem -1rem 0 -1rem; padding: 0;">
    """, unsafe_allow_html=True)

    # 主内容区域 - 垂直居中
    st.markdown("""
    <div style="flex: 1; display: flex; align-items: center; justify-content: center;
                padding: 40px 20px;">
    """, unsafe_allow_html=True)

    # 登录卡片
    st.markdown(f"""
    <div style="text-align: center; max-width: 450px; width: 100%;
                background: white; border-radius: 16px; padding: 50px 40px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                border: 1px solid #f1f5f9;">

        <!-- 标题 -->
        <h1 style="font-size: 32px; font-weight: 700; color: #1e293b;
                   margin-bottom: 16px; line-height: 1.2;">
            约苗<span style="color: #667eea;">智能</span>问答助手
        </h1>

        <!-- 副标题 -->
        <p style="font-size: 16px; color: #64748b; margin-bottom: 40px;
                  line-height: 1.6; font-weight: 400;">
            基于先进AI技术，为您提供专业、准确、高效的智能问答服务
        </p>

        <!-- 登录按钮 -->
        <a href="{URL}" target="_self"
           style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white; padding: 14px 28px; border-radius: 10px;
                  text-decoration: none; font-size: 16px; font-weight: 600;
                  display: inline-block; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
                  transition: all 0.2s ease; margin-bottom: 24px;">
            开始使用
        </a>

        <!-- 登录提示 -->
        <div style="font-size: 13px; color: #94a3b8; margin-bottom: 0;">
            💡 支持钉钉快速登录，安全便捷
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 关闭主内容区域
    st.markdown("</div>", unsafe_allow_html=True)

    # 底部信息 - 固定在底部
    st.markdown(f"""
    <div style="background: #f8fafc; border-top: 1px solid #e2e8f0;
                padding: 20px; text-align: center; margin-top: auto;">
        <div style="color: #64748b; font-size: 13px; line-height: 1.5; max-width: 600px; margin: 0 auto;">
            <div style="margin-bottom: 8px;">
                © {Year} <a href="{settings.business.URL}"
                   style="color: #667eea; text-decoration: none; font-weight: 500;">约苗科技</a>
            </div>
            <div style="font-size: 12px; color: #94a3b8;">
                🌟 让AI更好地服务于您 · 智能问答新体验
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 关闭全屏容器
    st.markdown("</div>", unsafe_allow_html=True)




def render_sidebar(app_config: AppConfig):
    """渲染侧边栏"""
    with st.sidebar:
        safe_markdown(f"""
            # {get_time_period()}，{st.session_state.userInfo.get("dt_name", "")}
            - `LLM： {st.session_state.llm['model']}`
            - `EMBEDDING： {st.session_state.emb['model']}`
            - `RETRIEVAL： {st.session_state.searchType}`
            - `TEMPERATURE：{st.session_state.temperature}`
            - `VERSION： {app_config.version}`
            """,
            allow_html=True
        )

        # 权限管理
        auth_manager = AuthManager()
        AUTH = auth_manager.check_auth(st.session_state.userInfo)
        auth_ids = st.session_state.curd.get_user_auth(
            role_id=st.session_state.userInfo.get("roles_id")
        )

        # 菜单生成
        Index = 2 if AUTH else 2
        SystemMenuItems = auth_manager.get_user_menu(auth_ids, AUTH)
        sac.menu(
            SystemMenuItems,
            open_index=1,
            key='menu',
            color='blue',
            index=Index
        )

@error_handler
def main():
    """主程序"""
    # 检查会话状态
    check_session_expired()
    
    # 初始化配置
    app_config = initialize_basic_config()
    
    # 初始化数据库连接
    st.session_state.curd = init_curd()

    # 登录验证
    Login(st.query_params.to_dict())

    # 渲染登录页面"
    if "userInfo" not in st.session_state:
        render_login_page_new(app_config)
        st.stop()

    # 初始化LLM
    if "llm" not in st.session_state:
        with_loading("正在初始化模型...")(init_llm)()
        logger.info(f"LLM初始化完成:{st.session_state}")

    # 初始化数据
    initialize_session_data()

    # 侧边栏
    render_sidebar(app_config)

    # 处理菜单
    handle_menu(st.session_state.menu)


if __name__ == "__main__":
    main()
