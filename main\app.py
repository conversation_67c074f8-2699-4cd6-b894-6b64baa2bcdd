#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: app.py
@time: 2024/9/29 15:07
@Description:
"""
import time
from typing import Dict, List
from dataclasses import dataclass

import streamlit as st
import streamlit_antd_components as sac

from common.myLog import logger
from config.config_center import settings
from common.initialize import init_data, Login, init_llm, init_curd
from common.utils import Year, get_time_period, error_handler, with_loading, safe_markdown
from const.enums import SystemMenuEnum
from const.style import centered_css
from const.menu_mapping import handle_menu



@dataclass
class AppConfig:
    """应用配置类"""
    page_config = settings.ui.get_page_config
    logo_config = settings.ui.get_logo_config
    login_url = settings.dingtalk.DingLoginUrl
    version = settings.version


class AuthManager:
    """用户认证管理类"""
    @staticmethod
    def check_auth(user_info: Dict) -> bool:
        return any([
            user_info.get("is_admin"),
            user_info.get("is_super")
        ])

    @staticmethod
    def get_user_menu(auth_ids: List[int], is_auth: bool) -> List:
        menu_system = SystemMenuEnum(auth_ids, is_auth)
        return [menu_system().get(i) for i in auth_ids if i in menu_system()]

def check_session_expired():
    """检查会话是否过期"""
    if 'last_activity' in st.session_state:
        if time.time() - st.session_state.last_activity > 7200:
            st.session_state.clear()
            st.info("会话已过期，请重新登录")
            st.stop()
    st.session_state.last_activity = time.time()


def initialize_basic_config():
    """初始化基础配置"""
    app_config = AppConfig()
    st.set_page_config(**app_config.page_config)
    st.logo(**app_config.logo_config)
    return app_config


def initialize_session_data():
    """初始化会话数据"""
    if "session_data_initialized" not in st.session_state:
        init_data()
        st.session_state.session_data_initialized = True


def render_login_page(app_config: AppConfig):
    """渲染登录页面"""
    safe_markdown(centered_css, allow_html=True)
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)
    safe_markdown(f"""
    <div class="body-container">
        <div>
            <h1>{settings.ui.rag_header}</h1>
            <a href="{URL}" target="_self" class="bt-link">开始使用</a>
        </div>
    </div>
    """, allow_html=True)
    safe_markdown(f"""
    <div class="ym-container">
        <span>© {Year} <a href="{settings.business.URL}" style="{settings.ui.ym_link_style}">约苗</a></span>
    </div>
    """, allow_html=True)


def render_login_page_new(app_config: AppConfig):
    """渲染新版登录页面 - 更美观的智能问答系统登录界面"""
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)

    # 使用单一HTML块来避免嵌套问题
    st.markdown(f"""
    <style>
        .login-button:hover {{
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }}
        .feature-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }}
    </style>

    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 50px 20px; margin: -1rem -1rem 0 -1rem; min-height: 90vh;
                display: flex; align-items: center; justify-content: center;">

        <div style="background: white; border-radius: 20px; padding: 40px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center;
                    max-width: 420px; width: 100%; margin: 0 auto; position: relative;">

            <!-- 顶部装饰条 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px;
                        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
                        border-radius: 20px 20px 0 0;"></div>

            <!-- AI图标 -->
            <div style="width: 70px; height: 70px; background: linear-gradient(135deg, #667eea, #764ba2);
                        border-radius: 15px; margin: 20px auto 25px; display: flex; align-items: center;
                        justify-content: center; font-size: 32px; color: white;
                        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
                        border: 3px solid rgba(255, 255, 255, 0.2);">🤖</div>

            <!-- 标题 -->
            <h1 style="font-size: 26px; font-weight: bold; color: #2d3748; margin-bottom: 8px;">
                约苗<span style="color: #667eea;">智能</span>问答助手
            </h1>

            <!-- 副标题 -->
            <p style="font-size: 15px; color: #718096; margin-bottom: 20px; line-height: 1.5;">
                🚀 基于先进AI技术，为您提供专业、准确、高效的智能问答服务
            </p>

            <!-- 版本信息 -->
            <div style="background: rgba(102, 126, 234, 0.1); border-radius: 20px;
                        padding: 8px 16px; margin-bottom: 25px; display: inline-block;">
                <span style="font-size: 12px; color: #667eea; font-weight: 500;">
                    ✨ {app_config.version} 版本
                </span>
            </div>

            <!-- 功能特性网格 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 30px;">
                <div style="background: #f7fafc; border-radius: 10px; padding: 18px 15px;
                            text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 20px; margin-bottom: 8px;">💡</div>
                    <div style="font-size: 13px; color: #4a5568; font-weight: 500;">智能理解</div>
                    <div style="font-size: 10px; color: #718096; margin-top: 3px;">自然语言处理</div>
                </div>
                <div style="background: #f7fafc; border-radius: 10px; padding: 18px 15px;
                            text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 20px; margin-bottom: 8px;">⚡</div>
                    <div style="font-size: 13px; color: #4a5568; font-weight: 500;">快速响应</div>
                    <div style="font-size: 10px; color: #718096; margin-top: 3px;">毫秒级回复</div>
                </div>
                <div style="background: #f7fafc; border-radius: 10px; padding: 18px 15px;
                            text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 20px; margin-bottom: 8px;">🎯</div>
                    <div style="font-size: 13px; color: #4a5568; font-weight: 500;">精准答案</div>
                    <div style="font-size: 10px; color: #718096; margin-top: 3px;">专业知识库</div>
                </div>
                <div style="background: #f7fafc; border-radius: 10px; padding: 18px 15px;
                            text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 20px; margin-bottom: 8px;">🔒</div>
                    <div style="font-size: 13px; color: #4a5568; font-weight: 500;">安全可靠</div>
                    <div style="font-size: 10px; color: #718096; margin-top: 3px;">企业级安全</div>
                </div>
            </div>

            <!-- 登录按钮 -->
            <a href="{URL}" target="_self" class="login-button"
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white; padding: 15px 35px; border-radius: 12px; text-decoration: none;
                      font-size: 16px; font-weight: 600; display: inline-flex; align-items: center;
                      gap: 8px; box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
                      transition: all 0.3s ease; margin-bottom: 15px;">
                <span style="font-size: 18px;">🚀</span>
                开始智能问答之旅
            </a>

            <!-- 快速入口提示 -->
            <div style="margin-bottom: 20px; font-size: 11px; color: #a0aec0;">
                💡 支持钉钉快速登录，安全便捷
            </div>

            <!-- 底部信息 -->
            <div style="padding-top: 20px; border-top: 1px solid #e2e8f0;
                        color: #718096; font-size: 12px; line-height: 1.4;">
                <div style="margin-bottom: 8px;">
                    © {Year} <a href="{settings.business.URL}"
                       style="color: #667eea; text-decoration: none; font-weight: 500;">约苗科技</a>
                </div>
                <div style="font-size: 10px; color: #a0aec0;">
                    🌟 让AI更好地服务于您 · 智能问答新体验
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 白色卡片容器 - 添加动画类
    st.markdown("""
    <div class="login-card" style="background: white; border-radius: 20px; padding: 40px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center;
                max-width: 420px; width: 100%; margin: 0 auto; position: relative;">
    """, unsafe_allow_html=True)

    # 顶部装饰条
    st.markdown("""
    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
                border-radius: 20px 20px 0 0;"></div>
    """, unsafe_allow_html=True)

    # AI图标 - 增强版
    st.markdown("""
    <div style="width: 70px; height: 70px; background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 15px; margin: 20px auto 25px; display: flex; align-items: center;
                justify-content: center; font-size: 32px; color: white;
                box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
                border: 3px solid rgba(255, 255, 255, 0.2);">🤖</div>
    """, unsafe_allow_html=True)

    # 标题 - 增强版
    st.markdown("""
    <h1 style="font-size: 26px; font-weight: bold; color: #2d3748; margin-bottom: 8px;
               text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        约苗<span style="background: linear-gradient(135deg, #667eea, #764ba2);
                    -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                    background-clip: text;">智能</span>问答助手
    </h1>
    """, unsafe_allow_html=True)

    # 副标题 - 增强版
    st.markdown("""
    <p style="font-size: 15px; color: #718096; margin-bottom: 25px; line-height: 1.5;">
        🚀 基于先进AI技术，为您提供专业、准确、高效的智能问答服务
    </p>
    """, unsafe_allow_html=True)

    # 版本信息
    st.markdown(f"""
    <div style="background: rgba(102, 126, 234, 0.1); border-radius: 20px;
                padding: 8px 16px; margin-bottom: 25px; display: inline-block;">
        <span style="font-size: 12px; color: #667eea; font-weight: 500;">
            ✨ {app_config.version} 版本
        </span>
    </div>
    """, unsafe_allow_html=True)

    # 功能特性 - 第一行 (增强版)
    st.markdown("""
    <div style="display: flex; gap: 12px; margin-bottom: 15px;">
        <div class="feature-card" style="background: linear-gradient(135deg, #f7fafc, #edf2f7);
                    border-radius: 10px; padding: 18px 15px; text-align: center; flex: 1;
                    border: 1px solid #e2e8f0; transition: all 0.3s ease; cursor: pointer;">
            <div style="font-size: 20px; margin-bottom: 8px;">💡</div>
            <div style="font-size: 13px; color: #4a5568; font-weight: 500;">智能理解</div>
            <div style="font-size: 10px; color: #718096; margin-top: 3px;">自然语言处理</div>
        </div>
        <div class="feature-card" style="background: linear-gradient(135deg, #f7fafc, #edf2f7);
                    border-radius: 10px; padding: 18px 15px; text-align: center; flex: 1;
                    border: 1px solid #e2e8f0; transition: all 0.3s ease; cursor: pointer;">
            <div style="font-size: 20px; margin-bottom: 8px;">⚡</div>
            <div style="font-size: 13px; color: #4a5568; font-weight: 500;">快速响应</div>
            <div style="font-size: 10px; color: #718096; margin-top: 3px;">毫秒级回复</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 功能特性 - 第二行 (增强版)
    st.markdown("""
    <div style="display: flex; gap: 12px; margin-bottom: 30px;">
        <div class="feature-card" style="background: linear-gradient(135deg, #f7fafc, #edf2f7);
                    border-radius: 10px; padding: 18px 15px; text-align: center; flex: 1;
                    border: 1px solid #e2e8f0; transition: all 0.3s ease; cursor: pointer;">
            <div style="font-size: 20px; margin-bottom: 8px;">🎯</div>
            <div style="font-size: 13px; color: #4a5568; font-weight: 500;">精准答案</div>
            <div style="font-size: 10px; color: #718096; margin-top: 3px;">专业知识库</div>
        </div>
        <div class="feature-card" style="background: linear-gradient(135deg, #f7fafc, #edf2f7);
                    border-radius: 10px; padding: 18px 15px; text-align: center; flex: 1;
                    border: 1px solid #e2e8f0; transition: all 0.3s ease; cursor: pointer;">
            <div style="font-size: 20px; margin-bottom: 8px;">🔒</div>
            <div style="font-size: 13px; color: #4a5568; font-weight: 500;">安全可靠</div>
            <div style="font-size: 10px; color: #718096; margin-top: 3px;">企业级安全</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 登录按钮 - 增强版
    st.markdown(f"""
    <a href="{URL}" target="_self" class="login-button"
       style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white; padding: 15px 35px; border-radius: 12px; text-decoration: none;
              font-size: 16px; font-weight: 600; display: inline-flex; align-items: center;
              gap: 8px; box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
              transition: all 0.3s ease; border: 2px solid transparent;">
        <span style="font-size: 18px;">🚀</span>
        开始智能问答之旅
    </a>
    """, unsafe_allow_html=True)

    # 快速入口提示
    st.markdown("""
    <div style="margin: 20px 0; font-size: 11px; color: #a0aec0;">
        💡 支持钉钉快速登录，安全便捷
    </div>
    """, unsafe_allow_html=True)

    # 底部信息 - 增强版
    st.markdown(f"""
    <div style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #e2e8f0;
                color: #718096; font-size: 12px; line-height: 1.4;">
        <div style="margin-bottom: 8px;">
            © {Year} <a href="{settings.business.URL}"
               style="color: #667eea; text-decoration: none; font-weight: 500;">约苗科技</a>
        </div>
        <div style="font-size: 10px; color: #a0aec0;">
            🌟 让AI更好地服务于您 · 智能问答新体验
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 关闭卡片容器
    st.markdown("</div>", unsafe_allow_html=True)

    # 关闭背景容器
    st.markdown("</div>", unsafe_allow_html=True)


def render_sidebar(app_config: AppConfig):
    """渲染侧边栏"""
    with st.sidebar:
        safe_markdown(f"""
            # {get_time_period()}，{st.session_state.userInfo.get("dt_name", "")}
            - `LLM： {st.session_state.llm['model']}`
            - `EMBEDDING： {st.session_state.emb['model']}`
            - `RETRIEVAL： {st.session_state.searchType}`
            - `TEMPERATURE：{st.session_state.temperature}`
            - `VERSION： {app_config.version}`
            """,
            allow_html=True
        )

        # 权限管理
        auth_manager = AuthManager()
        AUTH = auth_manager.check_auth(st.session_state.userInfo)
        auth_ids = st.session_state.curd.get_user_auth(
            role_id=st.session_state.userInfo.get("roles_id")
        )

        # 菜单生成
        Index = 2 if AUTH else 2
        SystemMenuItems = auth_manager.get_user_menu(auth_ids, AUTH)
        sac.menu(
            SystemMenuItems,
            open_index=1,
            key='menu',
            color='blue',
            index=Index
        )

@error_handler
def main():
    """主程序"""
    # 检查会话状态
    check_session_expired()
    
    # 初始化配置
    app_config = initialize_basic_config()
    
    # 初始化数据库连接
    st.session_state.curd = init_curd()

    # 登录验证
    Login(st.query_params.to_dict())

    # 渲染登录页面"
    if "userInfo" not in st.session_state:
        render_login_page_new(app_config)
        st.stop()

    # 初始化LLM
    if "llm" not in st.session_state:
        with_loading("正在初始化模型...")(init_llm)()
        logger.info(f"LLM初始化完成:{st.session_state}")

    # 初始化数据
    initialize_session_data()

    # 侧边栏
    render_sidebar(app_config)

    # 处理菜单
    handle_menu(st.session_state.menu)


if __name__ == "__main__":
    main()
