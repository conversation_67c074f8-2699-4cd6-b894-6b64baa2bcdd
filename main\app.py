#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: app.py
@time: 2024/9/29 15:07
@Description:
"""
import time
from typing import Dict, List
from dataclasses import dataclass

import streamlit as st
import streamlit_antd_components as sac

from common.myLog import logger
from config.config_center import settings
from common.initialize import init_data, Login, init_llm, init_curd
from common.utils import Year, get_time_period, error_handler, with_loading, safe_markdown
from const.enums import SystemMenuEnum
from const.style import centered_css
from const.menu_mapping import handle_menu



@dataclass
class AppConfig:
    """应用配置类"""
    page_config = settings.ui.get_page_config
    logo_config = settings.ui.get_logo_config
    login_url = settings.dingtalk.DingLoginUrl
    version = settings.version


class AuthManager:
    """用户认证管理类"""
    @staticmethod
    def check_auth(user_info: Dict) -> bool:
        return any([
            user_info.get("is_admin"),
            user_info.get("is_super")
        ])

    @staticmethod
    def get_user_menu(auth_ids: List[int], is_auth: bool) -> List:
        menu_system = SystemMenuEnum(auth_ids, is_auth)
        return [menu_system().get(i) for i in auth_ids if i in menu_system()]

def check_session_expired():
    """检查会话是否过期"""
    if 'last_activity' in st.session_state:
        if time.time() - st.session_state.last_activity > 7200:
            st.session_state.clear()
            st.info("会话已过期，请重新登录")
            st.stop()
    st.session_state.last_activity = time.time()


def initialize_basic_config():
    """初始化基础配置"""
    app_config = AppConfig()
    st.set_page_config(**app_config.page_config)
    st.logo(**app_config.logo_config)
    return app_config


def initialize_session_data():
    """初始化会话数据"""
    if "session_data_initialized" not in st.session_state:
        init_data()
        st.session_state.session_data_initialized = True


def render_login_page(app_config: AppConfig):
    """渲染登录页面"""
    safe_markdown(centered_css, allow_html=True)
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)
    safe_markdown(f"""
    <div class="body-container">
        <div>
            <h1>{settings.ui.rag_header}</h1>
            <a href="{URL}" target="_self" class="bt-link">开始使用</a>
        </div>
    </div>
    """, allow_html=True)
    safe_markdown(f"""
    <div class="ym-container">
        <span>© {Year} <a href="{settings.business.URL}" style="{settings.ui.ym_link_style}">约苗</a></span>
    </div>
    """, allow_html=True)


def render_login_page_new(app_config: AppConfig):
    """渲染新版登录页面 - 更美观的智能问答系统登录界面"""
    URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)

    # 新版登录页面样式
    login_new_css = """
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: -100px -50px;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            text-align: center;
            max-width: 480px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .login-title {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 12px;
            line-height: 1.2;
        }

        .login-subtitle {
            font-size: 16px;
            color: #718096;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .login-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-item {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            padding: 20px 16px;
            text-align: center;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .feature-text {
            font-size: 14px;
            color: #4a5568;
            font-weight: 500;
        }

        .login-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 18px 40px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            min-width: 200px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            color: #718096;
            font-size: 14px;
        }

        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 40px;
            height: 40px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .login-container {
                padding: 20px 15px;
                margin: -80px -20px;
            }

            .login-card {
                padding: 40px 30px;
                border-radius: 20px;
            }

            .login-title {
                font-size: 28px;
            }

            .login-features {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .login-button {
                padding: 16px 32px;
                font-size: 15px;
            }
        }
    </style>
    """

    safe_markdown(login_new_css, allow_html=True)

    # 渲染新版登录页面
    safe_markdown(f"""
    <div class="login-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="login-card">
            <div class="login-icon">
                🤖
            </div>

            <h1 class="login-title">
                约苗<span style="background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">智能</span>问答助手
            </h1>

            <p class="login-subtitle">
                基于先进AI技术，为您提供专业、准确、高效的智能问答服务
            </p>

            <div class="login-features">
                <div class="feature-item">
                    <span class="feature-icon">💡</span>
                    <div class="feature-text">智能理解</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⚡</span>
                    <div class="feature-text">快速响应</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎯</span>
                    <div class="feature-text">精准答案</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔒</span>
                    <div class="feature-text">安全可靠</div>
                </div>
            </div>

            <a href="{URL}" target="_self" class="login-button">
                <span>🚀</span>
                开始智能问答之旅
            </a>

            <div class="login-footer">
                © {Year} <a href="{settings.business.URL}">约苗科技</a> · 让AI更好地服务于您
            </div>
        </div>
    </div>
    """, allow_html=True)


def render_sidebar(app_config: AppConfig):
    """渲染侧边栏"""
    with st.sidebar:
        safe_markdown(f"""
            # {get_time_period()}，{st.session_state.userInfo.get("dt_name", "")}
            - `LLM： {st.session_state.llm['model']}`
            - `EMBEDDING： {st.session_state.emb['model']}`
            - `RETRIEVAL： {st.session_state.searchType}`
            - `TEMPERATURE：{st.session_state.temperature}`
            - `VERSION： {app_config.version}`
            """,
            allow_html=True
        )

        # 权限管理
        auth_manager = AuthManager()
        AUTH = auth_manager.check_auth(st.session_state.userInfo)
        auth_ids = st.session_state.curd.get_user_auth(
            role_id=st.session_state.userInfo.get("roles_id")
        )

        # 菜单生成
        Index = 2 if AUTH else 2
        SystemMenuItems = auth_manager.get_user_menu(auth_ids, AUTH)
        sac.menu(
            SystemMenuItems,
            open_index=1,
            key='menu',
            color='blue',
            index=Index
        )

@error_handler
def main():
    """主程序"""
    # 检查会话状态
    check_session_expired()
    
    # 初始化配置
    app_config = initialize_basic_config()
    
    # 初始化数据库连接
    st.session_state.curd = init_curd()

    # 登录验证
    Login(st.query_params.to_dict())

    # 渲染登录页面"
    if "userInfo" not in st.session_state:
        render_login_page(app_config)
        st.stop()

    # 初始化LLM
    if "llm" not in st.session_state:
        with_loading("正在初始化模型...")(init_llm)()
        logger.info(f"LLM初始化完成:{st.session_state}")

    # 初始化数据
    initialize_session_data()

    # 侧边栏
    render_sidebar(app_config)

    # 处理菜单
    handle_menu(st.session_state.menu)


if __name__ == "__main__":
    main()
